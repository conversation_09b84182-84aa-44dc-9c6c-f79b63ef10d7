<template>
  <a-drawer title="审核记录" width="680" v-model:open="visible" @close="handleClose">
    <div class="detailBox">
      <a-spin v-show="loading" />
      <div v-if="!loading && logList.length != 0" class="contentBox">
        <div class="detail" v-for="(item, index) in logList" :key="index">
          <div class="point">
            <div class="pointItem"></div>
          </div>
          <div class="detailTime">{{ item.op_at }}</div>
          <div class="detailContentBox">
            <div class="detailTitle">
              {{ item.user_name }}
              <span v-show="item.user_department" class="description">[{{ item.user_department }}]</span>
            </div>
            <!-- <div class="detailType">{{ item.op_type }}</div> -->
            <div class="detailItem" style="display: flex" v-for="(detail, detailIndex) in item.edits" :key="detailIndex">
              <div v-if="detail.name" style="white-space: nowrap">{{ detail.name }}：</div>
              <!-- <div class="oldVal">{{ detail.old_value ? detail.old_value : '' }}</div> -->
              <!-- <div class="to">></div> -->
              <div class="newVal">{{ detail.new_value }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="emptyText" v-show="!loading && logList.length === 0">该供应商无审核记录</div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { GetSupplierAuditInfo } from '@/servers/supplierSettlementApproval'

const visible = ref(false)
// const logContainerRef = ref<HTMLElement | null>(null)
const loading = ref(false)

const logList = ref<any[]>([])

// 清理字段名称，去除引号
// const cleanFieldName = (name: string) => {
//   if (!name) return ''
//   return name.replace(/"/g, '')
// }

// 清理字段值，处理空值和特殊字符
// const cleanFieldValue = (value: any) => {
//   if (value === null || value === undefined || value === '') {
//     // return '空'
//     return ''
//   }
//   if (typeof value === 'string') {
//     return value.replace(/"/g, '')
//   }
//   return String(value)
// }

const loadData = async (supplierId: number) => {
  loading.value = true
  try {
    const res: any = await GetSupplierAuditInfo({
      supplierId,
    })
    if (res.code === 0 && res.success) {
      logList.value = res.data || []
    }
  } catch (error) {
    console.error('加载审核记录失败:', error)
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
  logList.value = []
}

// 打开抽屉
const open = async (supplierId: number) => {
  visible.value = true
  await loadData(supplierId)
}

// 暴露方法给父组件调用
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.detailBox {
  width: 100%;
  margin-left: 30px;

  .drawer-title {
    width: calc(100% - 60px);
  }
}

.contentBox {
  padding: 5px 0 5px 20px;
  margin-left: 20px;
  border-left: 1px solid #409eff;

  .detail {
    position: relative;
    margin-bottom: 12px;

    .point {
      position: absolute;
      top: 2px;
      left: -28px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 15px;
      height: 15px;
      background-color: rgb(255 255 255);
      border: 1px solid #409eff;
      border-radius: 50%;

      .pointItem {
        width: 9px;
        height: 9px;
        background-color: #409eff;
        border-radius: 50%;
      }
    }

    .detailContentBox {
      padding: 16px;
      background: #f7f8fa;
      border-radius: 4px;

      .detailTitle {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        color: rgb(0 0 0 / 80%);
      }

      .description {
        padding-left: 10px;
        font-size: 12px;
        color: #999;
      }

      .detailType {
        margin-bottom: 8px;
        color: #999;
      }

      .detailItem {
        line-height: 20px;
        color: rgb(0 0 0 / 80%);

        .oldVal {
          height: auto;
          color: #999;
          text-decoration: line-through;
          word-break: break-all;
        }

        .newVal {
          height: auto;
          color: #000;
          word-break: break-all;
        }

        .to {
          margin: 0 10px;
          color: #666;
        }
      }
    }

    .detailTime {
      margin-bottom: 12px;
      font-size: 12px;
      color: #999;
    }
  }
}

.emptyText {
  padding: 40px 0;
  color: #999;
  text-align: center;
}
</style>
