import { ref, onMounted, onUnmounted } from 'vue'

interface UseIntersectionObserverOptions {
  threshold?: number
  rootMargin?: string
  root?: Element | null
}

export function useIntersectionObserver(
  options: UseIntersectionObserverOptions = {}
) {
  const {
    threshold = 0.1,
    rootMargin = '50px',
    root = null
  } = options

  const isIntersecting = ref(false)
  const elementRef = ref<HTMLElement | null>(null)
  let observer: IntersectionObserver | null = null

  const startObserving = () => {
    if (!elementRef.value || observer) return

    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          isIntersecting.value = entry.isIntersecting
        })
      },
      {
        threshold,
        rootMargin,
        root
      }
    )

    observer.observe(elementRef.value)
  }

  const stopObserving = () => {
    if (observer) {
      observer.disconnect()
      observer = null
    }
  }

  const setElement = (el: HTMLElement | null) => {
    if (elementRef.value === el) return
    
    stopObserving()
    elementRef.value = el
    
    if (el) {
      startObserving()
    }
  }

  onMounted(() => {
    if (elementRef.value) {
      startObserving()
    }
  })

  onUnmounted(() => {
    stopObserving()
  })

  return {
    isIntersecting,
    elementRef,
    setElement
  }
} 