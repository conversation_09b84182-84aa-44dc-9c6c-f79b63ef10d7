html[data-vxe-ui-theme='light'] {
  --vxe-ui-table-cell-padding-mini: 8px;
  --vxe-ui-table-cell-padding-left: 8px;
  --vxe-ui-table-cell-padding-right: 8px;
  --vxe-ui-font-color: #333;
  --vxe-ui-table-row-striped-background-color: #fafafb;
  --vxe-ui-table-header-background-color: #f8f8f9;
  --vxe-ui-table-border-color: #eaecee; // --vxe-ui-table-header-font-weight: 400;
}

// 头部
.vxe-header--row {
  .has-more .vxe-cell--title {
    padding-right: 14px;

    .anticon-more {
      position: absolute;
      top: 50%;
      right: 8px;
      margin-top: -7px;
      margin-left: auto;
    }
  }
}

// 内容
.vxe-table--render-default {
  .vxe-table--body-wrapper,
  .vxe-table--footer-wrapper {
    @include scrollBar;
  }

  .vxe-header--column {
    height: 40px !important;
  }

  .vxe-body--row.row--hover {
    background: linear-gradient(0deg, rgb(255 255 255 / 94%), rgb(255 255 255 / 94%)), #1890ff !important;

    .copy-cell-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    .link:not([class*='easy'], [class*='icon']) {
      text-decoration: underline;
    }
  }

  .vxe-body--column:not([class~='col--right'], [class~='col--center']) {
    .vxe-cell {
      padding: 0 2px 0 var(--vxe-ui-table-cell-padding-left) !important;
    }
  }

  .col--expand .vxe-cell {
    display: flex;

    .vxe-table--expanded {
      flex: 0 0 14px;
      width: 14px;

      & + .vxe-table--expand-label {
        padding-left: 3px;
      }
    }

    .vxe-table--expand-label {
      flex: 1;
      padding-left: 17px;
      overflow: hidden;
    }
  }

  .vxe-table--expanded .vxe-table--expand-btn {
    color: #999 !important;
  }
}

// 复制列 （divGrid共享）
.copy-cell {
  &-btn {
    display: none;
    width: 16px;
    height: 16px;
    font-size: 12px;
    color: #1890ff;
    background-color: #fff;
    border-radius: 2px;
  }
}

.tableBox .vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis > .vxe-cell,
.tableBox .vxe-table--render-default.size--mini .vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--title {
  text-overflow: clip;
}

// 图片下拉列表样式
.image-list-dropdown {
  .ant-dropdown-arrow {
    z-index: 100;
  }

  .image-list-dropdown-menu {
    background: #fff;
    border: 1px #1890ff solid;
    border-radius: 4px;
  }
}
