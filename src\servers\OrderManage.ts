// 订单管理
import { request } from './request'

// 获取订单列表
export const GetOrderList = (data) => {
  return request({ url: '/api/Order/GetList', data }, 'POST')
}

// 获取销售订单详情
export const GetSaleOrderDetail = (data) => {
  return request({ url: '/api/Order/Get', data }, 'GET')
}

// 手动同步订单数据
export const ManualSyncOrder = (data: { start_time: string; end_time: string }) => {
  const endTime = `${data.end_time} 23:59:59`
  return request(
    {
      url: '/api/Order/ManualSyncOrder',
      data: {
        start_time: data.start_time,
        end_time: endTime, // 添加当天的结束时间
      },
    },
    'POST',
  )
}
