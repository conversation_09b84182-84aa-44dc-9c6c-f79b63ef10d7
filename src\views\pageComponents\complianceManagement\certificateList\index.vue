<template>
  <div class="main">
    <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.QUALIFICATION_CERTIFICATE" @search="search" @setting="tableRef?.showTableSetting()"></SearchForm>
    <BaseTable ref="tableRef" :isCheckbox="true" :pageType="PageType.QUALIFICATION_CERTIFICATE" :get-list="getListFn" v-model:form="formArr" :auto-search="false" :is-index="true">
      <template #operate="{ row }">
        <a-button @click="handleAudit(row.id, 1)" type="text" :disabled="!btnPermission[81001]">查看</a-button>
        <a-button @click="handleAudit(row.id, 0)" type="text" :disabled="!btnPermission[81002] || row.approval_status !== 0">审核</a-button>
      </template>

      <template #right-btn>
        <a-select v-model:value="exportSelectValue" class="w-120px" :disabled="!btnPermission[81002]">
          <a-select-option v-for="item in exportSelect" :key="item.value" :value="item.value" :label="item.label">
            {{ item.label }}
          </a-select-option>
        </a-select>
        <a-button @click="handleExport" class="mr-8" :disabled="!btnPermission[81002]">导出</a-button>
      </template>

      <template #validity_period="{ row }">
        <span>{{ row.validity_period_start_time?.split(' ')[0] }} 至 {{ row.validity_period_end_time?.split(' ')[0] }}</span>
      </template>
      <template #file_count="{ row }">
        <span class="table-link" @click="handleViewFile(row)">{{ row.file_count }}</span>
      </template>
      <template #expired_status="{ row }">
        <span :class="getExpiredStatusClass(row.expired_status_str)">{{ row.expired_status_str }}</span>
      </template>
    </BaseTable>
    <AuditCertificate ref="auditCertificateRef" @search="search" />
    <ViewFile ref="viewFileRef" v-model:file-list="fileList" />
  </div>
</template>

<script setup lang="ts">
import * as pdfjsLib from 'pdfjs-dist/build/pdf'
import { ref } from 'vue'
import { PageType } from '@/common/enum'
import SearchForm from '@/components/SearchForm/index.vue'
import { getCommonOption } from '@/utils'
import { message } from 'ant-design-vue'
import { GetList, GetFileList } from '@/servers/Certificate'
import { Add as AddDownloadTask } from '@/servers/DownloadCenter'
import BaseTable from '@/components/BaseTable/index.vue'
import { ViewByFileIdCommon } from '@/servers/Common'
import { usePermission } from '@/hook/usePermission'
import AuditCertificate from './components/AuditCertificateDrawer.vue'
import ViewFile from './components/ViewFile.vue'

// 配置 PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'

const { btnPermission } = usePermission()
// 搜索功能
const search = () => tableRef.value.search()
const getListFn = async (params: any) => {
  params.is_get_total = true
  if (!tableRef.value?.ordersort) {
    params.sortField = 'modified_at'
    params.sortType = 'desc'
  }
  const res = await GetList(params)
  return {
    data: {
      list: res.data.list,
      total: res.data.total,
    },
  }
}
// 审核
const auditCertificateRef = ref()
// 查看文件
const viewFileRef = ref()
// 导出字段
const exportSelectValue = ref(1)
// 文件列表
const fileList = ref<any[]>([])
// 导出下拉框
const exportSelect = ref([
  {
    label: '导出全部',
    value: 1,
  },
  {
    label: '导出所选',
    value: 2,
  },
  {
    label: '导出筛选条件',
    value: 3,
  },
])
// 表格和表单引用
const tableRef = ref()
// 搜索表单配置
const formArr: any = ref([
  {
    label: '选择资质/证书',
    value: null,
    type: 'select',
    key: 'certificate_license_type',
  },
  {
    label: '生产商',
    value: null,
    type: 'input',
    key: 'manufacturer_name',
  },
  {
    label: '过期状态',
    value: null,
    type: 'select',
    key: 'expired_status',
  },
  {
    label: '审核状态',
    value: null,
    type: 'select',
    key: 'approval_status',
  },
  {
    label: '上传时间',
    value: null,
    type: 'range-picker',
    selectArr: [],
    key: 'upload_time',
    formKeys: ['modification_time_start', 'modification_time_end'],
    placeholder: ['上传开始日期', '上传结束日期'],
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
  },
])
// 审核
const handleAudit = (id: number, approval_status: number) => {
  auditCertificateRef.value.showDrawer(id, approval_status)
}
// 获取过期状态颜色
const getExpiredStatusClass = (status: string) => {
  switch (status) {
    case '已失效':
      return 'expired-status-expired'
    case '即将到期':
      return 'expired-status-soon'
    default:
      return ''
  }
}
// 提取 getPdfMinPreviewUrl
const getPdfMinPreviewUrl = async (fileUrl: string) => {
  return new Promise<string>((resolve) => {
    ;(async () => {
      const loadingTask = pdfjsLib.getDocument(fileUrl)
      const pdf = await loadingTask.promise
      const page = await pdf.getPage(1)
      const viewport = page.getViewport({ scale: 0.2 })
      const canvas = document.createElement('canvas')
      canvas.height = viewport.height
      canvas.width = viewport.width
      const context = canvas.getContext('2d')
      const renderContext = {
        canvasContext: context,
        viewport,
      }
      await page.render(renderContext).promise
      resolve(canvas.toDataURL('image/png'))
    })()
  })
}
// 查看文件
const handleViewFile = async (row: any) => {
  const fileIdList = row.file_ids.split(',')
  console.log('fileIdList', fileIdList)
  const params = {
    file_id_list: fileIdList,
  }

  const res = await GetFileList(params)
  const list = await Promise.all(
    res.data.map(async (i) => {
      console.log('i', i)
      const result = await ViewByFileIdCommon(i.id)
      // 根据 original_name 判断文件类型
      let fileType = 'image'
      if (i.original_name) {
        const fileName = i.original_name.toLowerCase()
        if (fileName.endsWith('.pdf')) {
          fileType = 'pdf'
        }
      }
      const url = URL.createObjectURL(result.data)
      let viewUrl = url
      // 如果是 pdf，生成缩略图
      if (fileType === 'pdf') {
        viewUrl = await getPdfMinPreviewUrl(url)
      }
      return {
        ...i,
        name: i.original_name,
        url: URL.createObjectURL(result.data),
        type: fileType,
        viewUrl,
      }
    }),
  )
  fileList.value = list
  console.log('fileList', fileList.value)
  viewFileRef.value.showModal(row)
}
// 导出
const handleExport = async () => {
  const ids = tableRef.value.checkItemsArr.map((item: any) => item.id)
  const uploadTime = formArr.value.find((item: any) => item.key === 'upload_time')?.value
  const params = {
    export_type: exportSelectValue.value,
    ids,
    filter: {
      certificate_license_type: formArr.value.find((item: any) => item.key === 'certificate_license_type')?.value,
      manufacturer_name: formArr.value.find((item: any) => item.key === 'manufacturer_name')?.value,
      expired_status: formArr.value.find((item: any) => item.key === 'expired_status')?.value,
      approval_status: formArr.value.find((item: any) => item.key === 'approval_status')?.value,
      modification_time_start: Array.isArray(uploadTime) && uploadTime[0] ? new Date(uploadTime[0]).toISOString() : undefined,
      modification_time_end: Array.isArray(uploadTime) && uploadTime[1] ? new Date(uploadTime[1]).toISOString() : undefined,
    },
  }
  if (exportSelectValue.value == 1) {
    delete (params as any).filter
    delete (params as any).ids
  }
  if (exportSelectValue.value == 2) {
    delete (params as any).filter
    if (ids.length == 0) {
      message.warning('请勾选要导出的资质证书')
      return
    }
  }
  if (exportSelectValue.value == 3) {
    delete (params as any).ids
  }

  try {
    // 显示导出准备中通知
    const taskId = `certificate_export_${Date.now()}`
    // showExportNotification({
    //   type: 'progress',
    //   title: '导出文件准备中',
    //   message: '正在处理您的资质证书导出请求，请稍候...',
    //   taskId,
    // })

    // 使用新的下载中心接口
    const downloadParams = {
      file_name: '资质证书导出',
      export_type_identifier: '2',
      export_params: params as any,
    }

    const res = await AddDownloadTask(downloadParams)
    if (res.success) {
      message.success('导出任务已添加到下载队列，请到下载中心查看进度')
    } else {
      message.error(res.message || '添加导出任务失败')
      // 如果任务添加失败，显示失败通知
      // showExportNotification({
      //   type: 'failed',
      //   title: '导出文件失败',
      //   message: res.message || '添加导出任务失败',
      //   taskId,
      // })
    }
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
    // 显示失败通知
    // showExportNotification({
    //   type: 'failed',
    //   title: '导出文件失败',
    //   message: '导出过程中发生错误，请重试',
    //   taskId: `certificate_export_${Date.now()}`,
    // })
  }
}
// 获取资质证书下拉筛选项
const getCertificateType = async () => {
  const [certificateOptions, expiredStatusOptions, auditStatusOptions] = await getCommonOption([16, 17, 18])
  formArr.value.forEach((item) => {
    if (item.key === 'certificate_license_type') {
      // 资质证书下拉数据
      item.options = certificateOptions
    }
    if (item.key === 'expired_status') {
      // 过期状态下拉数据
      item.options = expiredStatusOptions
    }
    if (item.key === 'approval_status') {
      // 审核状态下拉数据
      item.options = auditStatusOptions
    }
  })
}
onMounted(() => {
  search()
  getCertificateType()
})
</script>

<style lang="scss" scoped>
.expired-status-expired {
  color: #ff4d4f; // 红色
}

.expired-status-soon {
  color: #fd560f; // 橙色
}
</style>
