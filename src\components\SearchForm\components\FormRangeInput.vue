<template>
  <div class="flex items-center bg-#fff form-range-input">
    <a-input-number v-model:value="item.value[0]" class="w-127px" :min="0" :controls="false" :placeholder="item!.placeholder![0]" />
    <div class="px-8px c-#666">至</div>
    <a-input-number v-model:value="item.value[1]" class="w-127px" :min="0" :controls="false" :placeholder="item!.placeholder![1]" />
  </div>
</template>

<script setup lang="ts">
import { FormItemType } from '../type'

const item = defineModel<FormItemType<'range-input'>>('item', { required: true })
</script>

<style scoped lang="scss">
.form-range-input {
  border: 1px solid #dcdee0;
  border-radius: 4px;
  :deep(.ant-input-number) {
    border: none;
    box-shadow: none;
    &:focus {
      box-shadow: none;
      border: none;
      outline: none;
    }
  }
}
</style>
