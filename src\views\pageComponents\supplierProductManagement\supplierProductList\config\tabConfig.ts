// 供应商商品管理标签页配置接口定义：规范每个标签页的配置结构
export interface TabConfig {
  // 筛选条件配置：控制筛选区域显示哪些查询条件
  filterConfig: {
    hiddenFilters: string[] // 需要隐藏的筛选条件集合（存储筛选条件的key）
    visibleFilters: string[] // 需要显示的筛选条件集合（存储筛选条件的key）
  }

  // 表格列配置：控制表格的列显示、隐藏和排序
  columnConfig: {
    columnOrder: string[] // 表格列的显示顺序（按列的key排序）
    hiddenColumns: string[] // 需要隐藏的表格列集合（存储列的key）
    operateWidth: number // 操作列的宽度（单位：像素）
  }

  // 操作按钮配置：控制表格行级操作按钮的显示和权限
  operationConfig: {
    availableOperations: string[] // 当前标签页可用的操作按钮集合（如'view'表示查看）
    permissionMap: Record<string, number> // 操作按钮与权限ID的映射关系（键为操作名，值为权限ID）
  }
}

// 标签页配置映射：key为标签页对应的status值，value为该标签页的具体配置
export const tabConfigs: Record<number | string, TabConfig> = {
  // 全部标签页 (对应status: null)
  all: {
    filterConfig: {
      hiddenFilters: [], // 全部标签页不隐藏任何筛选条件
      visibleFilters: [
        // 全部标签页显示的筛选条件
        'supplier_product_number', // 供应商商品编码
        'product_number', // 平台商品编码
        'product_name', // 商品名称(中)
        'product_name_en', // 商品名称(英)
        'is_white_brand', // 是否白牌
        'brand_id_list', // 商品品牌
        'category_id_list', // 商品类目
        'declared_purchase_price', // 申报采购单价
        'agreed_purchase_price', // 议定采购单价
        'create_at', // 创建时间
        'modified_at', // 修改时间
        'supplier_number', // 供应商编码
        'supplier_name', // 供应商名称
        'selection_status', // 选品状态
        'plm_product_code', // PLM商品编码
        'jst_product_code', // 聚水潭商品编码
        'status', // 审核结果
      ],
    },
    columnConfig: {
      columnOrder: [
        // 全部标签页的列显示顺序
        'seq', // 序号
        'images_view_url', // 商品主图
        'supplier_product_number', // 供应商商品编码
        'product_number', // 平台商品编码
        'plm_demand_code', // PLM需求编码
        'product_name', // 商品名称(中)
        'product_name_en', // 商品名称(英)
        'is_white_brand_string', // 是否白牌
        'brand_name', // 商品品牌
        'category_string', // 商品类目
        'declared_purchase_tax_price', // 申报采购单价(含税)
        'agreed_purchase_tax_price', // 议定采购单价(含税)
        'selection_status_string', // 选品状态
        'selection_time', // 选品时间
        // 'selection_name', // 选品人
        'selection_notes', // 选品意见
        'supplier_name', // 供应商名称
        'supplier_number', // 供应商编码
        'submit_audit_name', // 提审人
        'submit_audit_at', // 提审时间
        'status_string', // 审核结果
        'audit_notes', // 备注
        'plm_product_code', // PLM商品编码
        'jst_product_code', // 聚水潭商品编码
        'create_at', // 创建时间
        'modified_at', // 修改时间
        'operate', // 操作
      ],
      hiddenColumns: ['plm_demand_code'], // 全部标签页隐藏PLM需求编码列
      operateWidth: 100, // 操作列宽度100px
    },
    operationConfig: {
      availableOperations: ['view'], // 可用操作：查看
      permissionMap: { view: 91101 }, // 查看操作对应的权限ID
    },
  },

  // 待选商品标签页 (对应status: 0)
  0: {
    filterConfig: {
      hiddenFilters: [
        // 需要隐藏的筛选条件
        'status', // 审核结果
        'product_number', // 平台商品编码
        'agreed_purchase_price', // 议定采购单价
        'selection_status', // 选品状态
        'plm_product_code', // PLM商品编码
        'jst_product_code', // 聚水潭商品编码
        'modified_at', // 修改时间
      ],
      visibleFilters: [
        // 需要显示的筛选条件
        'supplier_product_number', // 供应商商品编码
        'product_name', // 商品名称(中)
        'product_name_en', // 商品名称(英)
        'is_white_brand', // 是否白牌
        'brand_id_list', // 商品品牌
        'category_id_list', // 商品类目
        'declared_purchase_price', // 申报采购单价
        'create_at', // 创建时间
        'supplier_number', // 供应商编码
        'supplier_name', // 供应商名称
      ],
    },
    columnConfig: {
      columnOrder: [
        // 待选商品标签页的列显示顺序
        'seq', // 序号
        'images_view_url', // 商品主图
        'supplier_product_number', // 供应商商品编码
        'product_name', // 商品名称(中)
        'product_name_en', // 商品名称(英)
        'is_white_brand_string', // 是否白牌
        'brand_name', // 商品品牌
        'category_string', // 商品类目
        'declared_purchase_tax_price', // 申报采购单价(含税)
        'supplier_name', // 供应商名称
        'supplier_number', // 供应商编码
        'create_at', // 创建时间
        'modified_at', // 修改时间
        'operate', // 操作
      ],
      hiddenColumns: [
        // 需要隐藏的列
        'product_number', // 平台商品编码
        'agreed_purchase_tax_price', // 议定采购单价(含税)
        'selection_status_string', // 选品状态
        'selection_time', // 选品时间
        // 'selection_person', // 选品人
        'selection_notes', // 选品意见
        'submit_audit_name', // 提审人
        'submit_audit_at', // 提审时间
        'status', // 审核结果
        'audit_notes', // 备注
        'plm_product_code', // PLM商品编码
        'jst_product_code', // 聚水潭商品编码
        'plm_demand_code', // PLM需求编码
      ],
      operateWidth: 300, // 操作列宽度280px
    },
    operationConfig: {
      availableOperations: ['view', 'submit_audit', 'overrule', 'refuse'], // 可用操作：查看、提审、驳回、拒绝
      permissionMap: {
        view: 91201, // 查看操作对应的权限ID
        submit_audit: 91202, // 提审操作对应的权限ID
        overrule: 91203, // 驳回操作对应的权限ID
        refuse: 91204, // 拒绝操作对应的权限ID
      },
    },
  },

  // 选品审核中标签页 (对应status: 1)
  1: {
    filterConfig: {
      hiddenFilters: [
        // 选品审核中标签需要隐藏的筛选条件
        'product_number', // 平台商品编码
        'agreed_purchase_price', // 议定采购单价
        'selection_status', // 选品状态
        'plm_product_code', // PLM商品编码
        'jst_product_code', // 聚水潭商品编码
      ],
      visibleFilters: [
        // 选品审核中标签需要显示的筛选条件
        'supplier_product_number', // 供应商商品编码
        'product_number', // 平台商品编码
        'product_name', // 商品名称(中)
        'product_name_en', // 商品名称(英)
        'is_white_brand', // 是否白牌
        'brand_id_list', // 商品品牌
        'category_id_list', // 商品类目
        'declared_purchase_price', // 申报采购单价
        'agreed_purchase_price', // 议定采购单价
        'create_at', // 创建时间
        'modified_at', // 修改时间
        'supplier_number', // 供应商编码
        'supplier_name', // 供应商名称
        'submit_audit_name', // 提审人
        'submit_audit_time', // 提审开始时间
      ],
    },
    columnConfig: {
      columnOrder: [
        // 选品审核中标签页的列显示顺序
        'seq', // 序号
        'images_view_url', // 商品主图
        'supplier_product_number', // 供应商商品编码
        'product_name', // 商品名称(中)
        'product_name_en', // 商品名称(英)
        'is_white_brand_string', // 是否白牌
        'brand_name', // 商品品牌
        'category_string', // 商品类目
        'declared_purchase_tax_price', // 申报采购单价(含税)
        'supplier_name', // 供应商名称
        'supplier_number', // 供应商编码
        'submit_audit_name', // 提审人
        'submit_audit_at', // 提审时间
        'create_at', // 创建时间
        'modified_at', // 修改时间
        'operate', // 操作
      ],
      hiddenColumns: [
        // 选品审核中标签需要隐藏的列
        'product_number', // 平台商品编码
        'agreed_purchase_tax_price', // 议定采购单价(含税)
        'selection_status_string', // 选品状态
        'selection_time', // 选品时间
        // 'selection_person', // 选品人
        'status', // 审核结果
        'audit_notes', // 备注
        'plm_product_code', // PLM商品编码
        'jst_product_code', // 聚水潭商品编码
        'selection_audit_status', // 选品审核状态
        'selection_notes', // 选品意见
        'plm_demand_code', // PLM需求编码
      ],
      operateWidth: 100, // 操作列宽度100px
    },
    operationConfig: {
      availableOperations: ['view'], // 可用操作：查看
      permissionMap: { view: 91301 }, // 查看操作对应的权限ID
    },
  },

  // 完成选品标签页 (对应status: 3)
  3: {
    filterConfig: {
      hiddenFilters: [
        'selection_time', // 选品完成时间
      ], // 完成选品标签页不隐藏任何筛选条件
      visibleFilters: [
        // 需要显示的筛选条件
        'supplier_product_number', // 供应商商品编码
        'product_number', // 平台商品编码
        'product_name', // 商品名称(中)
        'product_name_en', // 商品名称(英)
        'is_white_brand', // 是否白牌
        'brand_id_list', // 商品品牌
        'category_id_list', // 商品类目
        'declared_purchase_price', // 申报采购单价
        'agreed_purchase_price', // 议定采购单价
        'create_at', // 创建时间
        'modified_at', // 修改时间
        'supplier_number', // 供应商编码
        'supplier_name', // 供应商名称
        'plm_product_code', // PLM商品编码
        'jst_product_code', // 聚水潭商品编码
        // 'selection_person', // 选品人(下拉框)
      ],
    },
    columnConfig: {
      columnOrder: [
        // 完成选品标签页的列显示顺序
        'seq', // 序号
        'images_view_url', // 商品主图
        'supplier_product_number', // 供应商商品编码
        'product_number', // 平台商品编码
        'plm_demand_code', // PLM需求编码
        'product_name', // 商品名称(中)
        'product_name_en', // 商品名称(英)
        'is_white_brand_string', // 是否白牌
        'brand_name', // 商品品牌
        'category_string', // 商品类目
        'declared_purchase_tax_price', // 申报采购单价(含税)
        'agreed_purchase_tax_price', // 议定采购单价(含税)
        'supplier_name', // 供应商名称
        'supplier_number', // 供应商编码
        'plm_product_code', // PLM商品编码
        'jst_product_code', // 聚水潭商品编码
        'create_at', // 创建时间
        'modified_at', // 修改时间
        'selection_notes', // 选品意见
        'operate', // 操作
      ],
      hiddenColumns: [
        // 需要隐藏的列
        'selection_name', // 选品人
        'selection_status_string', // 选品状态
        'submit_audit_name', // 提审人
        'submit_audit_at', // 提审时间
        'status', // 审核结果
        'audit_notes', // 备注
        'selection_time', // 完成选品时间(可排序)
      ],
      operateWidth: 100, // 操作列宽度100px
    },
    operationConfig: {
      availableOperations: ['view'], // 可用操作：查看
      permissionMap: { view: 91401 }, // 查看操作对应的权限ID
    },
  },

  // 已驳回标签页 (对应status: 2)
  2: {
    filterConfig: {
      hiddenFilters: [
        // 需要隐藏的筛选条件
        'product_number', // 平台商品编码
        'agreed_purchase_price', // 议定采购单价
        'selection_status', // 选品状态
        'plm_product_code', // PLM商品编码
        'jst_product_code', // 聚水潭商品编码
      ],
      visibleFilters: [
        // 需要显示的筛选条件
        'supplier_product_number', // 供应商商品编码
        'product_number', // 平台商品编码
        'product_name', // 商品名称(中)
        'product_name_en', // 商品名称(英)
        'is_white_brand', // 是否白牌
        'brand_id_list', // 商品品牌
        'category_id_list', // 商品类目
        'declared_purchase_price', // 申报采购单价
        'agreed_purchase_price', // 议定采购单价
        'create_at', // 创建时间
        'modified_at', // 修改时间
        'supplier_number', // 供应商编码
        'supplier_name', // 供应商名称
        // 'selection_person', // 选品人(下拉框)
      ],
    },
    columnConfig: {
      columnOrder: [
        // 已驳回标签页的列显示顺序
        'seq', // 序号
        'images_view_url', // 商品主图
        'supplier_product_number', // 供应商商品编码
        'product_name', // 商品名称(中)
        'product_name_en', // 商品名称(英)
        'is_white_brand_string', // 是否白牌
        'brand_name', // 商品品牌
        'category_string', // 商品类目
        'declared_purchase_tax_price', // 申报采购单价(含税)

        'supplier_name', // 供应商名称
        'supplier_number', // 供应商编码

        'selection_notes', // 选品意见
        'create_at', // 创建时间
        'modified_at', // 修改时间
        'operate', // 操作
      ],
      hiddenColumns: [
        // 已驳回需要隐藏的列
        'refuse_selection_time', // 拒绝选品时间
        'selection_name', // 选品人
        'agreed_purchase_tax_price', // 议定采购单价(含税)
        'selection_status_string', // 选品状态
        'selection_time', // 选品时间（这里显示的是拒绝选品时间）
        'submit_audit_name', // 提审人
        'submit_audit_at', // 提审时间
        'status', // 审核结果
        'audit_notes', // 备注
        'plm_product_code', // PLM商品编码
        'jst_product_code', // 聚水潭商品编码
        'product_number', // 平台商品编码
        'refuse_selection_time', // 拒绝选品时间
        'plm_demand_code', // PLM需求编码
      ],
      operateWidth: 100, // 操作列宽度100px
    },
    operationConfig: {
      availableOperations: ['view'], // 可用操作：查看
      permissionMap: { view: 91501 }, // 查看操作对应的权限ID
    },
  },

  // 已拒绝标签页 (对应status: 4)
  4: {
    filterConfig: {
      hiddenFilters: [
        // 需要隐藏的筛选条件
        'product_number', // 平台商品编码
        'agreed_purchase_price', // 议定采购单价
        'selection_status', // 选品状态
        'plm_product_code', // PLM商品编码
        'jst_product_code', // 聚水潭商品编码
      ],
      visibleFilters: [
        // 需要显示的筛选条件
        'supplier_product_number', // 供应商商品编码
        'product_number', // 平台商品编码
        'product_name', // 商品名称(中)
        'product_name_en', // 商品名称(英)
        'is_white_brand', // 是否白牌
        'brand_id_list', // 商品品牌
        'category_id_list', // 商品类目
        'declared_purchase_price', // 申报采购单价
        'agreed_purchase_price', // 议定采购单价
        'create_at', // 创建时间
        'modified_at', // 修改时间
        'supplier_number', // 供应商编码
        'supplier_name', // 供应商名称
        // 'selection_person', // 选品人(下拉框)
        'refuse_selection_time', // 拒绝选品时间
      ],
    },
    columnConfig: {
      columnOrder: [
        // 已拒绝标签页的列显示顺序
        'seq', // 序号
        'images_view_url', // 商品主图
        'supplier_product_number', // 供应商商品编码
        'product_name', // 商品名称(中)
        'product_name_en', // 商品名称(英)
        'is_white_brand_string', // 是否白牌
        'brand_name', // 商品品牌
        'category_string', // 商品类目
        'declared_purchase_tax_price', // 申报采购单价(含税)
        'supplier_name', // 供应商名称
        'supplier_number', // 供应商编码
        'refuse_selection_time', // 拒绝选品时间
        // 'selection_name', // 选品人
        'selection_notes', // 选品意见
        'create_at', // 创建时间
        'modified_at', // 修改时间
        'operate', // 操作
      ],
      hiddenColumns: [
        // 需要隐藏的列
        'selection_status_string', // 选品状态
        'selection_time', // 选品时间（这里显示的是拒绝选品时间）
        'submit_audit_name', // 提审人
        'submit_audit_at', // 提审时间
        'status', // 审核结果
        'audit_notes', // 备注
        'plm_product_code', // PLM商品编码
        'jst_product_code', // 聚水潭商品编码
        'agreed_purchase_tax_price', // 议定采购单价(含税)
        'plm_demand_code', // PLM需求编码
      ],
      operateWidth: 100, // 操作列宽度100px
    },
    operationConfig: {
      availableOperations: ['view'], // 可用操作：查看
      permissionMap: { view: 91601 }, // 查看操作对应的权限ID
    },
  },
}

// 获取当前标签页配置：根据status值返回对应的标签页配置
// 参数：status - 标签页状态（null表示"全部"，0表示"待选商品"，1表示"选品审核中"等）
// 返回值：对应状态的标签页配置，默认返回"全部"标签页配置
export function getCurrentTabConfig(status: number | null): TabConfig {
  const key = status === null ? 'all' : status // 确定配置映射的key（null对应'all'）
  const config = tabConfigs[key] || tabConfigs.all // 找不到对应配置时返回默认的"全部"配置

  // 获取标签页配置的调试信息
  // console.log('📋 获取标签页配置:', {
  //   status,
  //   key,
  //   hasConfig: !!tabConfigs[key],
  //   visibleFiltersCount: config.filterConfig.visibleFilters.length,
  //   hiddenFiltersCount: config.filterConfig.hiddenFilters.length,
  //   visibleFilters: config.filterConfig.visibleFilters,
  //   hiddenFilters: config.filterConfig.hiddenFilters,
  // })

  return config
}

// 筛选项配置定义：定义所有可能的筛选项配置
export const filterItemConfigs = {
  supplier_product_number: {
    label: '供应商商品编码',
    value: null,
    type: 'inputDlg',
    width: 240,
    key: 'supplier_product_number',
    isShow: true,
  },
  product_number: {
    label: '平台商品编码',
    value: null,
    type: 'inputDlg',
    width: 240,
    key: 'product_number',
    isShow: true,
  },
  product_name: {
    label: '商品名称(中)',
    value: null,
    type: 'input',
    key: 'product_name',
    isShow: true,
  },
  product_name_en: {
    label: '商品名称(英)',
    value: null,
    type: 'input',
    key: 'product_name_en',
    isShow: true,
  },
  is_white_brand: {
    label: '是否白牌',
    value: null,
    type: 'select',
    key: 'is_white_brand',
    multiple: false,
    options: [
      {
        value: true,
        label: '是',
      },
      {
        value: false,
        label: '否',
      },
    ],
    isShow: true,
    onChange: null, // 将在运行时设置联动逻辑
  },
  brand_id_list: {
    label: '商品品牌',
    value: [],
    type: 'select',
    key: 'brand_id_list',
    multiple: true,
    options: [],
    isShow: true,
  },
  category_id_list: {
    label: '商品类目',
    value: [],
    type: 'cascader',
    key: 'category_id_list',
    multiple: true,
    options: [],
    fieldNames: { label: 'label', value: 'value', children: 'children' },
    isShow: true,
  },
  declared_purchase_price: {
    label: '申报采购单价',
    type: 'range-input',
    key: 'declared_purchase_price',
    formKeys: ['min_declared_purchase_tax_price', 'max_declared_purchase_tax_price'],
    value: [],
    placeholder: ['最小申报采购单价', '最大申报采购单价'],
    width: 300,
    isShow: true,
  },
  agreed_purchase_price: {
    label: '议定采购单价',
    type: 'range-input',
    key: 'agreed_purchase_price',
    value: [],
    // formKeys: ['min_agreed_purchase_price', 'max_agreed_purchase_price'],
    formKeys: ['min_agreed_purchase_tax_price', 'max_agreed_purchase_tax_price'],
    placeholder: ['最小议定采购单价', '最大议定采购单价'],
    width: 320,
    isShow: true,
  },
  create_at: {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'create_at',
    formKeys: ['create_at_start', 'create_at_end'],
    placeholder: ['创建开始日期', '创建结束日期'],
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
    isShow: true,
  },
  modified_at: {
    label: '最后修改时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'modified_at',
    formKeys: ['modified_at_start', 'modified_at_end'],
    placeholder: ['修改开始日期', '修改结束日期'],
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
    isShow: true,
  },
  supplier_number: {
    label: '供应商编码',
    value: null,
    type: 'inputDlg',
    width: 240,
    key: 'supplier_number',
    isShow: true,
  },
  supplier_name: {
    label: '供应商名称',
    value: null,
    type: 'input',
    key: 'supplier_name',
    isShow: true,
  },
  selection_status: {
    label: '选品状态',
    value: null,
    type: 'select',
    key: 'selection_status',
    options: [],
    isShow: true,
  },
  plm_product_code: {
    label: 'PLM商品编码',
    value: null,
    type: 'inputDlg',
    width: 240,
    key: 'plm_product_code',
    isShow: true,
  },
  jst_product_code: {
    label: '聚水潭商品编码',
    value: null,
    type: 'inputDlg',
    width: 240,
    key: 'jst_product_code',
    isShow: true,
  },
  status: {
    label: '审核结果',
    value: null,
    key: 'status',
    type: 'select',
    isShow: true,
    isQuicks: true,
    multiple: false,
    quickNotFull: true,
    options: [
      { label: '审核通过', value: 20 },
      { label: '审核驳回', value: 30 },
      { label: '审核拒绝', value: 40 },
    ],
  },
  submit_audit_name: {
    label: '提审人',
    value: null,
    type: 'select',
    key: 'submit_audit_name',
    options: [], // 需要从API获取提审人下拉选项
    isShow: true,
  },
  submit_audit_time: {
    label: '提审开始时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'submit_audit_time',
    formKeys: ['submit_audit_time_start', 'submit_audit_time_end'],
    placeholder: ['提审开始日期', '提审结束日期'],
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
    isShow: true,
  },
  // selection_person: {
  //   label: '选品人',
  //   value: null,
  //   type: 'select',
  //   key: 'selection_person',
  //   options: [], // 需要从API获取选品人下拉选项
  //   isShow: true,
  // },
  selection_time: {
    label: '选品完成时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'selection_time',
    formKeys: ['selection_time_start', 'selection_time_end'],
    placeholder: ['选品开始日期', '选品结束日期'],
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
    isShow: true,
  },
  refuse_selection_time: {
    label: '拒绝选品时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'refuse_selection_time',
    formKeys: ['refuse_selection_time_start', 'refuse_selection_time_end'],
    placeholder: ['拒绝选品开始日期', '拒绝选品结束日期'],
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
    isShow: true,
  },
}

// 根据标签页配置生成对应的 formArr
// 参数：status - 标签页状态
// 返回值：该标签页对应的筛选项数组
export function generateFormArrByStatus(status: number | null): any[] {
  const config = getCurrentTabConfig(status)
  const visibleFilters = config.filterConfig.visibleFilters

  const formArr = visibleFilters
    .map((filterKey) => {
      const baseConfig = filterItemConfigs[filterKey as keyof typeof filterItemConfigs]
      if (!baseConfig) {
        console.warn(`⚠️ 未找到筛选项配置: ${filterKey}`)
        return null
      }

      // 深拷贝配置，避免修改原始配置
      return JSON.parse(JSON.stringify(baseConfig))
    })
    .filter(Boolean) // 过滤掉 null 值

  // 生成标签页筛选项的调试信息
  // console.log('🔧 生成标签页筛选项:', {
  //   status,
  //   visibleFiltersCount: visibleFilters.length,
  //   generatedFormArrCount: formArr.length,
  //   visibleFilters,
  //   formArrKeys: formArr.map((item) => item.key),
  // })

  return formArr
}
