$color: #1890ff;

@mixin clearfix {
  &::after {
    display: table;
    clear: both;
    content: '';
  }
}

@mixin flex($justify: center, $align: center, $direction: row, $wrap: no-wrap) {
  display: flex;
  flex-flow: $direction $wrap;
  align-items: $align;
  justify-content: $justify;
}

@mixin scrollBar {
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}

@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

@mixin pct($pct) {
  position: relative;
  width: #{$pct};
  margin: 0 auto;
}

@mixin bgSize($url, $size: 100% 100%, $pos: center top) {
  background: url($url) no-repeat $pos;
  background-size: $size;
}

// 模糊蒙层
@mixin blurMask($rgba: 0.7, $blur: 5px) {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100vw;
  height: 100vh;

  &::after {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    content: '';
    background: rgba(0, 0, 0, $rgba);
    backdrop-filter: blur($blur);
    opacity: 0;
    animation: opacityFrame 0.3s ease-in-out;
    animation-fill-mode: forwards;

    @keyframes opacityFrame {
      from {
        opacity: 0;
      }

      to {
        opacity: 1;
      }
    }
  }
}

@mixin module($width, $height) {
  position: absolute;
  top: 50%;
  left: 50%;
  width: $width;
  height: $height;
  background: #fff;
  transform: translate(-50%, -50%);
  animation: opacityFrame 0.3s ease-in-out;
}
