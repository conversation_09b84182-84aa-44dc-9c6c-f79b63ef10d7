<template>
  <div class="login-middle">
    <div class="loader"></div>
    <div class="title">SRS供应商管理后台加载中...</div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { LoginCallback } from '@/servers/UmcAuth'
import { message } from 'ant-design-vue/es'
import eventBus from '@/utils/eventBus'

const router = useRouter()
const route = useRoute()

// 错误状态管理
const errorCode = ref(1000)
const errorMessage = ref('')
// 处理UMC登录回调
const handleUmcCallback = async () => {
  try {
    // 优先从localStorage获取参数
    let code = localStorage.getItem('code') || ''
    let scope = localStorage.getItem('umc_scope') || ''
    let state = localStorage.getItem('umc_state') || ''
    let session_state = localStorage.getItem('umc_session_state') || ''

    // 如果localStorage为空，从URL和route.query获取
    if (!code || !scope || !state || !session_state) {
      const urlParams = new URLSearchParams(window.location.search)

      if (!code) code = urlParams.get('code') || (route.query.code as string) || ''
      if (!scope) scope = urlParams.get('scope') || (route.query.scope as string) || ''
      if (!state) state = urlParams.get('state') || (route.query.state as string) || ''
      if (!session_state) session_state = urlParams.get('session_state') || (route.query.session_state as string) || ''
    }

    // URL解码参数（特别处理scope参数中的加号）
    if (scope) {
      scope = decodeURIComponent(scope.replace(/\+/g, ' '))
    }
    if (state) {
      state = decodeURIComponent(state.replace(/\+/g, ' '))
    }
    if (session_state) {
      session_state = decodeURIComponent(session_state.replace(/\+/g, ' '))
    }

    // 调试信息
    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('Loading页面最终使用的参数:', {
        code: code ? `${code.substring(0, 20)}...` : '',
        scope,
        state,
        session_state,
      })
    }

    if (!code) {
      message.error('登录参数错误，请重新登录')
      router.push('/login')
      return
    }

    // 调用登录回调接口
    const res = await LoginCallback({
      code,
      scope,
      state,
      session_state,
    })

    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('LoginCallback响应:', res)
    }

    if (res.success) {
      // 清除临时存储的UMC回调参数
      localStorage.removeItem('code')
      localStorage.removeItem('umc_scope')
      localStorage.removeItem('umc_state')
      localStorage.removeItem('umc_session_state')
      localStorage.removeItem('umc_iss')

      // 清理URL中的回调参数和UmcAuth/LoginCallback路径
      if (window.location.search || window.location.pathname.includes('/UmcAuth/LoginCallback')) {
        // 构建新的URL，去掉UmcAuth/LoginCallback路径和查询参数
        let newPathname = window.location.pathname
        if (newPathname.includes('/UmcAuth/LoginCallback')) {
          newPathname = '/'
        }
        const newUrl = window.location.origin + newPathname + window.location.hash
        window.history.replaceState({}, document.title, newUrl)

        if (import.meta.env.VITE_APP_ENV === 'development') {
          console.log('清理URL路径:', {
            原始URL: window.location.href,
            新URL: newUrl,
          })
        }
      }

      // 添加登录时间戳用于token过期检查
      const userDataWithTimestamp = {
        ...res.data,
        login_time: Math.floor(Date.now() / 1000),
      }

      // 存储用户数据
      localStorage.setItem('userData', JSON.stringify(userDataWithTimestamp))
      localStorage.setItem('edition', import.meta.env.VITE_APP_VERSION)
      // 设置自动登录标识，避免页面刷新时清除用户数据
      localStorage.setItem('autoLogin', 'true')

      if (!localStorage.getItem('screeningObj')) {
        localStorage.setItem('screeningObj', JSON.stringify({}))
      }

      const menuList = res.data.permissions_infos

      // 智能路由跳转逻辑
      setTimeout(() => {
        let targetPage = '/'

        // 定义系统中实际存在的路由路径（只包含具体页面，不包含一级目录）
        const existingRoutes = [
          // 系统管理模块
          '/roleManagement',
          '/watermarkManagement',
          // 用户管理模块
          '/userLists',
          // 供应商商品管理模块
          '/supplierProductList',
          '/supplierProductAuditList',
          // 合规管理模块
          '/certificateList',
          // 仓库管理模块
          '/warehouse',
          // 供应商管理模块
          '/supplierSettlementApproval',
          '/supplierInfo',
          // 商品管理模块
          '/brand',
          '/attr',
          '/categoryTemplate',
          '/category',
          '/productLabel',
          // 通知管理模块
          '/systemNotify',
          // 基础资料管理模块
          '/countryRegion',
          '/currency',
          '/exchangeRate',
          '/language',
          '/productLibrary',
          '/certificateList',
          '/basicInfoManagement',
          '/supplierAdminManagement',
        ]

        // 智能匹配可用路由的函数
        const findValidRoute = (menuList: any[]) => {
          if (!menuList || menuList.length === 0) {
            return '/'
          }

          // 遍历菜单列表，寻找第一个存在的路由
          for (const menu of menuList) {
            // 检查子菜单
            if (menu.children && menu.children.length > 0) {
              for (const child of menu.children) {
                if (child.path && existingRoutes.includes(child.path)) {
                  return child.path
                }
              }
            }
            // 检查父菜单路径
            if (menu.path && existingRoutes.includes(menu.path)) {
              return menu.path
            }
          }

          // 如果没有找到匹配的路由，返回第一个存在的路由作为默认值
          return '/'
        }

        if (menuList && menuList.length > 0) {
          targetPage = findValidRoute(menuList)

          // 如果没有找到有效的权限路由，跳转到错误页面
          if (targetPage === '/') {
            targetPage = '/404'
          }
        } else {
          // 没有菜单权限时，跳转到错误页面
          targetPage = '/404'
        }

        // 跳转到目标页面
        if (targetPage && targetPage !== '/') {
          router.push(targetPage)
        } else {
          router.push('/')
        }

        // 设置水印
        try {
          eventBus.emit('setWatermark')
        } catch (error) {
          console.warn('设置水印失败:', error)
        }
      }, 500)
    } else {
      // 去掉权限校验，即使接口返回失败也尝试保存用户数据并登录
      if (res.data && res.data.login_token) {
        // 如果有用户数据，保存并继续登录流程
        const userDataWithTimestamp = {
          ...res.data,
          login_time: Math.floor(Date.now() / 1000),
        }

        localStorage.setItem('userData', JSON.stringify(userDataWithTimestamp))
        localStorage.setItem('edition', import.meta.env.VITE_APP_VERSION)
        // 设置自动登录标识，避免页面刷新时清除用户数据
        localStorage.setItem('autoLogin', 'true')

        if (!localStorage.getItem('screeningObj')) {
          localStorage.setItem('screeningObj', JSON.stringify({}))
        }

        setTimeout(() => {
          const menuList = res.data.permissions_infos
          let targetPage = '/'

          if (menuList && menuList.length > 0) {
            // 有菜单权限，跳转到首页让index.vue处理
            targetPage = '/'
          } else {
            // 没有菜单权限，直接跳转到404页面
            targetPage = '/404'
          }

          router.push(targetPage)

          try {
            eventBus.emit('setWatermark')
          } catch (error) {
            console.warn('设置水印失败:', error)
          }
        }, 500)
      } else {
        // 如果完全没有用户数据，才处理错误
        await handleLoginError(res)
      }
    }
  } catch (error: any) {
    console.error('UMC登录回调处理失败:', error)

    // 检查是否是特定的错误代码
    const errorResponse = error?.response?.data || error
    if (errorResponse?.code === 1000 || errorResponse?.code === 3000) {
      // 对于权限错误，如果有token就强制登录，否则跳转到404
      if (errorResponse?.data?.login_token) {
        const userDataWithTimestamp = {
          ...errorResponse.data,
          login_time: Math.floor(Date.now() / 1000),
        }

        localStorage.setItem('userData', JSON.stringify(userDataWithTimestamp))
        localStorage.setItem('edition', import.meta.env.VITE_APP_VERSION)
        // 设置自动登录标识，避免页面刷新时清除用户数据
        localStorage.setItem('autoLogin', 'true')

        // 直接跳转到404页面，因为没有菜单权限
        router.push('/404')
      } else {
        await handleLoginError(errorResponse)
      }
    } else {
      message.error('登录失败，请重试')
      router.push('/404')
      // router.push('/login')
    }
  }
}
// 保持原有的登录处理逻辑
const handleLogin = async () => {
  await handleUmcCallback()
}

// 使用watch监听路由变化，保持原有逻辑
watch(
  route,
  () => {
    handleLogin()
  },
  {
    immediate: true,
  },
)

// 处理登录错误
const handleLoginError = async (errorResponse: any) => {
  const code = errorResponse?.code || 1000
  const msg = errorResponse?.message || '登录失败'

  if (import.meta.env.VITE_APP_ENV === 'development') {
    console.log('处理登录错误:', { code, msg })
  }

  // 检查是否是需要显示错误页面的错误代码
  if (code === 1000 || code === 3000) {
    // 对于权限相关错误，先保存token到sessionStorage以便后续退出登录使用
    if (errorResponse?.data?.login_token) {
      sessionStorage.setItem('logoutLoginToken', errorResponse.data.login_token)
      if (import.meta.env.VITE_APP_ENV === 'development') {
        console.log('保存LoginToken到sessionStorage用于退出登录:', `${errorResponse.data.login_token.substring(0, 20)}...`)
      }
    } else {
      // 如果响应中没有token，尝试从localStorage获取
      const userData = localStorage.getItem('userData')
      if (userData) {
        try {
          const userDataObj = JSON.parse(userData)
          const LoginToken = userDataObj.login_token || ''
          if (LoginToken) {
            sessionStorage.setItem('logoutLoginToken', LoginToken)
            if (import.meta.env.VITE_APP_ENV === 'development') {
              console.log('从localStorage获取LoginToken并保存到sessionStorage:', `${LoginToken.substring(0, 20)}...`)
            }
          }
        } catch (e) {
          console.warn('解析userData失败:', e)
        }
      }
    }
    errorCode.value = code
    errorMessage.value = msg
    // showError.value = true
    router.push('/404')
  } else {
    // 其他错误直接显示消息并跳转
    message.error(msg || '登录失败')
    // router.push('/login')
  }
}
</script>

<style scoped lang="scss">
.login-middle {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f4f7f9;
}

.login-middle.hidden {
  pointer-events: none;
  visibility: hidden;
  opacity: 0;
  transition: all 0.8s ease-out;
}

.dark .loading {
  background: #0d0d10;
}

.title {
  margin-top: 66px;
  font-size: 28px;
  font-weight: 600;
  color: rgb(0 0 0 / 85%);
}

.dark .title {
  color: #fff;
}

.loader {
  position: relative;
  width: 48px;
  height: 48px;
}

.loader::before {
  position: absolute;
  top: 60px;
  left: 0;
  width: 48px;
  height: 5px;
  content: '';
  /* 原来的蓝色阴影 */
  background: hsl(var(--primary, 210 100% 50%) / 50%);
  /* 修改后的紫色阴影 */
  background: hsl(270deg 100% 50% / 50%);
  border-radius: 50%;
  animation: shadow-ani 0.5s linear infinite;
}

.loader::after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  content: '';

  // background: hsl(var(--primary, 210 100% 50%));
  background: hsl(270deg 100% 50%);
  border-radius: 4px;
  animation: jump-ani 0.5s linear infinite;
}

@keyframes jump-ani {
  15% {
    border-bottom-right-radius: 3px;
  }

  25% {
    transform: translateY(9px) rotate(22.5deg);
  }

  50% {
    border-bottom-right-radius: 40px;
    transform: translateY(18px) scale(1, 0.9) rotate(45deg);
  }

  75% {
    transform: translateY(9px) rotate(67.5deg);
  }

  100% {
    transform: translateY(0) rotate(90deg);
  }
}

@keyframes shadow-ani {
  0%,
  100% {
    transform: scale(1, 1);
  }

  50% {
    transform: scale(1.2, 1);
  }
}
</style>
