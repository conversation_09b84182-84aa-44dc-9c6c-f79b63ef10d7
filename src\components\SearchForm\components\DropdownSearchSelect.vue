<template>
  <a-select
    :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
    v-model:value="item.value"
    :placeholder="item.placeholder"
    allow-clear
    show-search
    :filter-option="false"
    :loading="loading"
    :options="displayOptions"
    class="w-200px"
    @search="handleSearch"
    @change="handleChange"
    @dropdown-visible-change="handleDropdownVisibleChange"
  >
    <template #notFoundContent>
      <div v-if="loading" class="text-center py-2">
        <a-spin size="small" />
        <span class="ml-2">{{ loadingText }}</span>
      </div>
      <div v-else-if="searchValue && displayOptions.length === 0" class="text-center py-2 text-gray-500">{{ noSearchResultText }}</div>
      <div v-else-if="!searchValue && displayOptions.length === 0" class="text-center py-2 text-gray-500">{{ noDataText }}</div>
    </template>
  </a-select>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { FormItemType } from '../type'

// 通用数据项接口
interface DataItem {
  [key: string]: any
}

// 选项配置接口
interface OptionConfig {
  labelField: string // 显示标签的字段名
  valueField: string // 值字段名
  searchFields: string[] // 搜索时要匹配的字段名数组
  labelFormatter?: (item: DataItem) => string // 自定义标签格式化函数
}

// 组件 Props
interface Props {
  // 数据加载函数（可选，与 options 二选一）
  loadData?: () => Promise<{ success: boolean; data?: DataItem[] }>
  // 直接传入的选项数据（可选，与 loadData 二选一）
  options?: DataItem[]
  // 选项配置
  optionConfig?: OptionConfig
  // 提示文本配置
  loadingText?: string
  noDataText?: string
  noSearchResultText?: string
  // 是否预加载数据（仅在使用 loadData 时有效）
  preload?: boolean
  // 防抖延迟时间
  debounceDelay?: number
}

// 简单的防抖函数实现
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
  let timeout: number | null = null
  return ((...args: any[]) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }) as T
}

defineEmits<{
  (e: 'search'): void
}>()

const props = withDefaults(defineProps<Props>(), {
  loadingText: '加载中...',
  noDataText: '暂无数据',
  noSearchResultText: '未找到相关数据',
  preload: true,
  debounceDelay: 300,
})

const item = defineModel<FormItemType<'dropdown-search-select'>>('item', { required: true })

// 状态管理
const loading = ref(false)
const searchValue = ref('')
const displayOptions = ref<Array<{ label: string; value: any }>>([])

// 数据缓存
const allData = ref<DataItem[]>([])

// 获取有效的配置，优先使用 props，然后使用 item 中的配置
const getEffectiveOptions = () => {
  const options = props.options || (item.value as any).options || []

  return options
}

const getEffectiveOptionConfig = (): OptionConfig => {
  const config = props.optionConfig || (item.value as any).optionConfig
  if (!config) {
    // 提供默认配置
    return {
      labelField: 'label',
      valueField: 'value',
      searchFields: ['label', 'value'],
    }
  }
  return config
}

// 加载所有数据
const loadAllData = async () => {
  // 优先使用有效的 options 数据
  const effectiveOptions = getEffectiveOptions()
  if (effectiveOptions && effectiveOptions.length > 0) {
    allData.value = effectiveOptions
    updateDisplayOptions()
    return
  }

  // 如果没有传入 options 且已经加载过数据，不重复加载
  if (allData.value.length > 0) return

  // 如果没有 loadData 函数，无法加载数据
  if (!props.loadData) {
    console.warn('DropdownSearchSelect: 既没有传入 options 也没有提供 loadData 函数')
    return
  }

  loading.value = true
  try {
    const res = await props.loadData()
    if (res.success && res.data) {
      allData.value = res.data
      // 加载完成后默认显示所有选项
      updateDisplayOptions()
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 更新显示选项
const updateDisplayOptions = (keyword: string = '') => {
  const optionConfig = getEffectiveOptionConfig()
  let filteredData = allData.value

  // 如果有搜索关键词，进行过滤
  if (keyword && keyword.trim().length > 0) {
    const searchTerm = keyword.trim().toLowerCase()
    filteredData = allData.value.filter((item) => {
      return optionConfig.searchFields.some((field) => {
        const fieldValue = item[field]
        if (fieldValue == null) return false
        return fieldValue.toString().toLowerCase().includes(searchTerm)
      })
    })
  }

  // 转换为选项格式
  displayOptions.value = filteredData.map((item) => ({
    label: optionConfig.labelFormatter ? optionConfig.labelFormatter(item) : item[optionConfig.labelField],
    value: item[optionConfig.valueField],
  }))
}

// 防抖搜索
const debouncedSearch = debounce(updateDisplayOptions, props.debounceDelay)

// 处理搜索输入
const handleSearch = async (value: string) => {
  searchValue.value = value

  // 如果还没有加载数据，先加载
  if (allData.value.length === 0) {
    await loadAllData()
  }

  debouncedSearch(value)
}

// 处理选择变化
const handleChange = (value: any) => {
  // 选择变化时的处理逻辑
  console.log('选中的值:', value)

  // 如果清空了选择，重新显示所有选项
  if (!value && allData.value.length > 0) {
    updateDisplayOptions()
    searchValue.value = ''
  }
}

// 处理下拉框显示/隐藏
const handleDropdownVisibleChange = async (visible: boolean) => {
  if (visible) {
    // 下拉框打开时，如果没有数据就加载，如果没有搜索内容就显示所有选项
    if (allData.value.length === 0) {
      await loadAllData()
    } else if (!searchValue.value) {
      updateDisplayOptions()
    }
  }
}

// 监听 options 变化（包括 props.options 和 item.options）
watch(
  () => [props.options, (item.value as any).options],
  () => {
    const effectiveOptions = getEffectiveOptions()
    if (effectiveOptions && effectiveOptions.length > 0) {
      allData.value = effectiveOptions
      updateDisplayOptions()
    }
  },
  { immediate: true, deep: true },
)

// 初始化时如果有值，需要加载对应的选项
watch(
  () => item.value.value,
  async (newValue) => {
    if (newValue && allData.value.length === 0) {
      // 如果有初始值但没有数据，先加载数据
      await loadAllData()
    }

    if (newValue && allData.value.length > 0) {
      const optionConfig = getEffectiveOptionConfig()
      // 根据值查找对应的数据项
      const dataItem = allData.value.find((item) => item[optionConfig.valueField] === newValue)
      if (dataItem) {
        displayOptions.value = [
          {
            label: optionConfig.labelFormatter ? optionConfig.labelFormatter(dataItem) : dataItem[optionConfig.labelField],
            value: dataItem[optionConfig.valueField],
          },
        ]
      }
    }
  },
  { immediate: true },
)

// 组件挂载时预加载数据
onMounted(() => {
  // 如果有有效的 options，优先使用
  const effectiveOptions = getEffectiveOptions()
  if (effectiveOptions && effectiveOptions.length > 0) {
    allData.value = effectiveOptions
    updateDisplayOptions()
  } else if (props.preload && props.loadData) {
    // 否则如果设置了预加载且有 loadData 函数，则预加载
    loadAllData()
  }
})
</script>

<style scoped>
.w-200px {
  width: 200px;
}
</style>
