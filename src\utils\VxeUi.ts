import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'
import CopyBtn from '@/components/CopyBtn/index.vue'
import BaseBadge from '@/components/BaseBadge/index.vue'
import { BadgeType } from '@/components/BaseBadge/type'
import { h, withDirectives } from 'vue'
import { vCopy } from '@/directive/copy'
import BaseImage from '@/components/BaseImage/index.vue'
import { number2 } from '.'

// 通用的空值处理formatter函数
const defaultFormatter = ({ cellValue }) => ([null, undefined, ''].includes(cellValue) ? '--' : cellValue)

// 金额格式化函数，添加￥符号
const priceFormatter = ({ cellValue }) => {
  if ([null, undefined, ''].includes(cellValue)) return '--'
  const numValue = Number(cellValue)
  return Number.isNaN(numValue) ? '--' : `￥${numValue.toFixed(2)}`
}

VxeUI.formats.add('number', {
  tableCellFormatMethod: ({ cellValue }) => number2(cellValue, 2),
  tableFooterCellFormatMethod: ({ itemValue }) => number2(itemValue, 2),
})

/** 用于在单元格复制按钮操作 */
VxeUI.renderer.add('copy', {
  renderTableDefault: (_renderOpts, renderParams) => {
    const value = renderParams.row[renderParams.column.field]
    return h('div', {}, [h('span', {}, value || '--'), value && withDirectives(h(CopyBtn), [[vCopy, String(value)]])])
  },
})
VxeUI.renderer.add('badge', {
  renderTableDefault: (_renderOpts, renderParams) => {
    const value = renderParams.row[renderParams.column.field]
    if (!value) return h('span', {}, '--')
    let type: BadgeType = 'default'
    if (/拒绝|超时|未通过|不通过|失败|禁用/.test(value)) type = 'error'
    else if (/未|待/.test(value)) type = 'warning'
    else if (/默认|中|进行/.test(value)) type = 'info'
    else if (/通过|成功|完成|确认|完全|正常|启用/.test(value)) type = 'success'
    else if (/作废|取消|关闭/.test(value)) type = 'default'
    return h(BaseBadge, { label: value, type })
  },
})

/**
 * 用于列表图片预览的渲染器，在vxe-table配置传cellRender: { name: 'image', props: { fileIdKey: 'logo_id' }
 * name：必填，固定为'image'
 * props：非必填，场景在于，列表没有返回预览路径，需要通过文件id去查询文件预览路径，那么fileIdKey对应的value值就是列表内对应文件id那个字段
 */
VxeUI.renderer.add('image', {
  renderTableDefault: (_renderOpts, renderParams) => {
    const fileId = _renderOpts!.props!.fileIdKey ? renderParams.row[_renderOpts!.props!.fileIdKey] : null
    const value = renderParams.row[renderParams.column.field]
    return h(BaseImage, { src: value || null, id: fileId })
  },
})
export default VxeUI

export { defaultFormatter, priceFormatter }
