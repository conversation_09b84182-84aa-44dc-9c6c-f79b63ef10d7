<template>
  <div class="role-group-left-panel" :class="{ collapsed }">
    <div class="header" v-show="!collapsed">
      <span class="title">属性分组</span>
    </div>
    <div class="collapse-button" @click="toggleCollapse">
      <DoubleLeftOutlined v-if="!collapsed" />
      <DoubleRightOutlined v-else />
    </div>

    <div class="panel-content" v-show="!collapsed">
      <!-- 分组列表 -->
      <div class="group-list">
        <a-spin :spinning="loading">
          <div class="group-item-container">
            <!-- 所有分组（包括API返回的"全部"选项） -->
            <div
              v-for="group in filteredGroupList"
              :key="group.id || 'all'"
              class="group-item"
              :class="{ active: selectedGroupId === (group.id || 'all') }"
              @click="handleSelectGroup(group.id || 'all')"
            >
              <div class="group-content">
                <div class="group-name" :title="group.attr_group_name">{{ group.attr_group_name }}</div>
                <div class="group-count">({{ group.attr_count || 0 }})</div>
              </div>
            </div>
          </div>
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { DoubleLeftOutlined, DoubleRightOutlined } from '@ant-design/icons-vue'

interface AttrGroup {
  id: string | number
  attr_group_name: string
  attr_count: number
  is_enabled: boolean
}

const props = defineProps<{
  groupList: AttrGroup[]
  selectedGroupId: string | number | null
  loading?: boolean
  collapsed: boolean
}>()

const emit = defineEmits<{
  selectGroup: [groupId: string | number]
  createGroup: []
  editGroup: [group: AttrGroup]
  deleteGroup: [group: AttrGroup]
  search: [value: string]
  'update:collapsed': [collapsed: boolean]
}>()

const searchValue = ref('')
const filteredGroupList = ref<AttrGroup[]>([])

// 过滤分组
const filterGroups = () => {
  if (!searchValue.value) {
    filteredGroupList.value = props.groupList
  } else {
    filteredGroupList.value = props.groupList.filter((group) => group.attr_group_name.toLowerCase().includes(searchValue.value.toLowerCase()))
  }
}

// 监听分组列表变化，更新过滤列表
watch(
  () => props.groupList,
  (newList) => {
    filterGroups()
  },
  { immediate: true },
)

// 监听搜索值变化
watch(searchValue, () => {
  filterGroups()
})

// 选择分组
const handleSelectGroup = (groupId: string | number) => {
  emit('selectGroup', groupId)
}

// 切换折叠状态
const toggleCollapse = () => {
  emit('update:collapsed', !props.collapsed)
}
</script>

<style lang="scss" scoped>
.role-group-left-panel {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 240px;
  height: 100%;
  padding: 16px;
  background: #fff;
  border-right: 1px solid #e8e8e8;
  transition: all 0.3s;

  &.collapsed {
    width: 0;
    padding: 0;
    overflow: visible; /* 确保折叠按钮在面板折叠时可见 */
    border-right: none;
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .title {
      font-size: 12px;
      font-weight: normal;
      line-height: 20px;
      color: #333;
      letter-spacing: 0;
    }
  }

  .collapse-button {
    position: absolute;
    top: 50%;
    right: -14px;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 14px;
    height: 40px;
    cursor: pointer;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-left: none;
    transform: translateY(-50%);

    &:hover {
      background-color: #f8f8f9;
    }

    .anticon {
      font-size: 11px;
      color: #999;
    }
  }

  .panel-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
  }

  .group-list {
    flex: 1;
    margin-bottom: 16px;
    overflow-y: auto;

    .group-item-container {
      .group-item {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        margin-bottom: 4px;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.2s;

        &:hover {
          background-color: #f1f8ff;
          border: 1px solid #d0e8ff;

          .group-actions {
            opacity: 1;
          }
        }

        &.active {
          background-color: #e8f4ff;
          border: 1px solid #d0e8ff;

          .group-name,
          .group-count {
            color: #3fa3ff !important;
          }
        }

        .group-content {
          display: flex;
          flex: 1;
          align-items: center;
          min-width: 0;

          .group-name {
            font-size: 12px;
            font-weight: normal;
            line-height: 20px;
            color: #333;
            letter-spacing: 0;
          }

          .group-count {
            margin-left: 4px;
            font-size: 12px;
            color: #999;
            white-space: nowrap;
          }
        }

        .group-actions {
          display: flex;
          gap: 8px;
          align-items: center;
          opacity: 0;
          transition: opacity 0.2s;

          .action-icon {
            font-size: 14px;
            color: #666;
            cursor: pointer;
            transition: color 0.2s;

            &:hover {
              color: #1890ff;
            }

            &.delete-icon:hover {
              color: #ff4d4f;
            }
          }
        }
      }
    }
  }
}

// 滚动条样式
.group-list::-webkit-scrollbar {
  width: 4px;
}

.group-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.group-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}
</style>
