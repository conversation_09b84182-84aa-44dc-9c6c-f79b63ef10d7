import { Dropdown, message } from 'ant-design-vue'
import { CopyOutlined } from '@ant-design/icons-vue'
import { defineAsyncComponent } from 'vue'

import EasyImage from '@/components/EasyImage.vue'
// 列表图片渲染
export const imageListRender = (_, params: any) => {
  const { column, row, options, key, size } = params
  const imgSize = { 36: 28, 56: 40, 76: 60 }[size] || 28
  let boxWidth: number
  let maxHeight: number

  // 获取订单商品数据
  const orderItems = row[key || column.field] || []
  const totalCount = orderItems.length

  // 获取前4个商品的图片ID，用于显示缩略图
  const imageIds = orderItems
    .slice(0, 5)
    .map((item: any) => item.main_images_id)
    .filter((id: any) => id)

  return h(
    Dropdown,
    {
      color: '#fff',
      trigger: ['click'],
      overlayStyle: {
        zIndex: 400,
      },
      overlayClassName: 'image-list-dropdown',
      destroyPopupOnHide: true,
      placement: 'bottomLeft',
      arrow: { pointAtCenter: true },
      align: {
        offset: [0, -5],
        overflow: { adjustX: true, adjustY: true },
      },
      autoAdjustOverflow: true,
      openChange: (flag: boolean) => {
        if (!flag) {
          // CG回收
          boxWidth = 0
          maxHeight = 0
        }
      },
    },
    {
      overlay: () =>
        h(
          'div',
          {
            class: 'image-list-dropdown-menu',
            style: {
              width: `${boxWidth}px`,
              overflow: 'hidden',
              zIndex: 1,
              maxWidth: '53vw',
            },
          },
          options.render({ column, row, maxHeight }),
        ),
      default: () => {
        return h(
          'div',
          {
            class: 'flex items-center cursor-pointer',
            onClick: (event: MouseEvent) => {
              event.stopPropagation()
              const ele = (event.target as HTMLElement).closest('.ant-dropdown-trigger')
              if (!ele) return
              const rect = ele.getBoundingClientRect()
              const domCenterPointX = rect.left + rect.width / 2
              const max = window.innerWidth * 0.4
              if (domCenterPointX > max) {
                boxWidth = domCenterPointX - 20
              } else {
                boxWidth = window.innerWidth - domCenterPointX - 20
              }
              maxHeight = event.clientY - 100
            },
          },
          [
            // 显示缩略图
            ...imageIds.slice(0, 5).map((fileId: number, index: number) => {
              const EasyImageById = defineAsyncComponent(() => import('@/components/EasyImageById.vue'))
              const orderItem = orderItems[index]
              return h(
                'div',
                {
                  style: {
                    position: 'relative',
                    width: '40px',
                    height: '40px',
                    marginRight: '4px',
                  },
                },
                [
                  h(EasyImageById, {
                    fileId,
                    w: 40,
                    h: 40,
                    hidePreview: true,
                    imgStyle: { objectFit: 'cover', borderRadius: '4px' },
                  }),
                  // 在图片左上角显示数量
                  h(
                    'div',
                    {
                      style: {
                        position: 'absolute',
                        top: '0',
                        left: '0',
                        display: 'flex',
                        flexDirection: 'column',
                        padding: '1px 4px',
                        background: '#FFFFFF',
                        boxSizing: 'border-box',
                        border: '1px solid #DCDEE0',
                        borderRadius: '5px',
                        fontSize: '10px',
                        lineHeight: '12px',
                        color: '#333333',
                        minWidth: '16px',
                        textAlign: 'center',
                        zIndex: 1,
                      },
                    },
                    orderItem?.qty?.toString() || '1',
                  ),
                ],
              )
            }),
            // 如果没有图片，显示默认图片
            ...(imageIds.length === 0
              ? [
                  h(
                    'div',
                    {
                      style: {
                        position: 'relative',
                        width: '40px',
                        height: '40px',
                        marginRight: '4px',
                      },
                    },
                    [
                      h('img', {
                        src: '/src/assets/icons/default-product.svg',
                        alt: '默认图片',
                        style: {
                          width: '40px',
                          height: '40px',
                          objectFit: 'cover',
                          borderRadius: '4px',
                        },
                      }),
                    ],
                  ),
                ]
              : []),
            // 显示总数量（在最右边）
            h(
              'div',
              {
                style: {
                  marginLeft: 'auto',
                  marginRight: '-18px',
                  padding: '2px 8px',
                  background: '#FFFFFF',
                  boxSizing: 'border-box',
                  border: '1px solid #DCDEE0',
                  borderRadius: '8px',
                  fontSize: '12px',
                  lineHeight: '16px',
                  color: '#333333',
                  minWidth: '20px',
                  textAlign: 'center',
                },
              },
              totalCount.toString(),
            ),
          ],
        )
      },
    },
  )
}

// 文案渲染
export const textRender = (_, params) => {
  const { column, row, options } = params
  const value = row[column.field]
  const rawOptions = column.cellRender ? column.cellRender.options : options || {}

  const _options: Record<string, any> = rawOptions && typeof rawOptions === 'object' && !Array.isArray(rawOptions) ? rawOptions : {}
  let text = value
  const color = _options.defaultColor || ''
  let style: Record<string, any> = { ...(_options.style || {}) }
  // 支持对象数据自定义样式
  if (typeof value === 'object' && value !== null) {
    text = value.text || value.label || value.value || ''
    if (value.color) style.color = value.color
    if (value.style) style = { ...style, ...value.style }
    if (value.bold) style.fontWeight = 'bold'
  }
  if (color) style.color = color
  if (_options.bold) style.fontWeight = 'bold'
  const tooltip: any = inject('tooltip')
  return h(
    'span',
    {
      style,
      onMouseenter: (event: MouseEvent) => {
        const node = (event.target as HTMLElement)?.parentNode as HTMLElement
        if (!node || (node.classList.contains('vxe-cell') && node.classList.contains('c--tooltip'))) return
        if (node.scrollWidth > node.clientWidth) {
          tooltip.open(
            {
              ...event,
              target: node,
            },
            text,
          )
        }
      },
      onMouseleave: () => {
        tooltip.close()
      },
    },
    [
      rawOptions?.icon
        ? h('span', {
            class: ['cursor-pointer mr-5', rawOptions.icon],
            onClick: () => rawOptions.iconClick({ row, column, cellValue: row[column.field] }),
          })
        : null,
      rawOptions?.textFormat ? rawOptions.textFormat({ cellValue: text }) : text,
    ],
  )
}

// 图片渲染
export const imageRender = (_, params) => {
  const imgSize = { 36: 28, 56: 40, 76: 60 }[params.column.cellRender?.height || params.$table?.props?.rowConfig?.height] || 28
  const src = params.row[params.column.field]
  return h(EasyImage, { src, h: imgSize, w: imgSize })
}

// 通过文件ID渲染图片
export const imageByIdRender = (_, params) => {
  const imgSize = { 36: 28, 56: 40, 76: 60 }[params.column.cellRender?.height || params.$table?.props?.rowConfig?.height] || 28
  const fileId = params.row[params.column.field]
  // 动态导入EasyImageById组件
  const EasyImageById = defineAsyncComponent(() => import('@/components/EasyImageById.vue'))
  return h(EasyImageById, { fileId, h: imgSize, w: imgSize })
}

// 复制渲染
export const copyRender = (_, params) => {
  const { column, row, options } = params
  const value = row[column.field]
  const rawOptions = column.cellRender ? column.cellRender.options : options || {}
  const valueState = value instanceof Array ? value.join(',') : value
  if (valueState && valueState !== '' && valueState !== ' ' && valueState !== ',') {
    return h('span', { class: 'w-full copy-cell flex items-center' }, [
      h('span', { class: 'overflow-hidden' }, [valueState]),
      rawOptions?.suffix,
      valueState !== '...' &&
        h(
          'span',
          { class: 'copy-cell-btn ml-auto mr-4' },
          h(CopyOutlined, {
            onClick: async () => {
              try {
                if (navigator.clipboard && window.isSecureContext) {
                  await navigator.clipboard.writeText(valueState)
                } else {
                  const input = document.createElement('input')
                  input.value = valueState
                  document.body.appendChild(input)
                  input.focus()
                  input.select()
                  document.execCommand('copy')
                  document.body.removeChild(input)
                }
                message.success('复制成功')
              } catch (error) {
                console.error('复制失败:', error)
                message.error('复制失败，请手动复制')
              }
            },
          }),
        ),
    ])
  }
  return ''
}
