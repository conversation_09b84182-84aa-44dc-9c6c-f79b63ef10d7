<template>
  <a-modal v-model:open="visible" :title="modalTitle" @ok="handleSubmit" @cancel="handleCancel" :confirm-loading="loading" width="500px" :okText="btnText">
    <div class="audit-modal-content">
      <div class="mb-16px">
        <label class="block text-sm font-medium text-gray-700 mb-8px">
          审核意见
          <span v-if="auditType === 'reject'" class="text-red-500">*</span>
        </label>

        <!-- 提示语区域 -->
        <div v-if="auditType === 'reject'" class="text-gray-500 text-xs mb-8px">若内容不符合要求，审核人可驳回并反馈修改意见，提审人调整后可重新提交审核。</div>

        <a-textarea v-model:value="formData.audit_opinion" placeholder="" :rows="4" :maxlength="200" show-count />
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { SupplierAudit } from '@/servers/supplierSettlementApproval'

interface FormData {
  audit_opinion: string
}

const emit = defineEmits<{
  success: []
}>()

const visible = ref(false)
const loading = ref(false)
const auditType = ref('')
const supplierId = ref<number>(0)

const formData = ref<FormData>({
  audit_opinion: '',
})

const btnText = computed(() => {
  return auditType.value === 'pass' ? '通过' : '驳回'
})

const modalTitle = computed(() => {
  return auditType.value === 'pass' ? '审核通过' : '审核驳回'
})

// 显示弹窗
const showModal = (id: number, type: string) => {
  supplierId.value = id
  auditType.value = type
  formData.value.audit_opinion = ''
  visible.value = true
}

// 取消
const handleCancel = () => {
  visible.value = false
  formData.value.audit_opinion = ''
}

// 提交
const handleSubmit = async () => {
  if (auditType.value === 'reject' && !formData.value.audit_opinion.trim()) {
    message.error('请输入审核意见')
    return
  }

  loading.value = true

  try {
    const params = {
      supplier_id: supplierId.value,
      is_pass: auditType.value === 'pass',
      audit_opinion: formData.value.audit_opinion.trim(),
    }

    await SupplierAudit(params)
    message.success('提交成功')

    visible.value = false
    emit('success')
  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件调用
defineExpose({
  showModal,
})
</script>

<style scoped lang="scss">
.audit-modal-content {
  padding: 8px 0;
}

.text-red-500 {
  color: #ef4444;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-500 {
  color: #6b7280;
}

.mb-16px {
  margin-bottom: 16px;
}

.mb-8px {
  margin-bottom: 8px;
}

.block {
  display: block;
}

.text-sm {
  font-size: 14px;
}

.font-medium {
  font-weight: 500;
}

.text-xs {
  font-size: 12px;
}
</style>
