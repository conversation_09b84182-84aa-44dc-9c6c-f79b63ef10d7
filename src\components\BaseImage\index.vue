<template>
  <div ref="imageContainerRef" class="h-full flex-shrink-0 base-image relative" :style="autoStyle">
    <loading-outlined v-if="loading" class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 c-primary" />
    <img v-else class="h-full w-full" :src="previewUrl || src || ErrorIcon" :preview="!!src || !!previewUrl" />
    <div class="base-image-mask" v-if="!!src || !!previewUrl" @click="() => setVisible(true)">
      <EyeOutlined class="text-white absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
    </div>
    <a-image
      v-if="!!src || !!previewUrl"
      :style="{ display: 'none' }"
      :preview="{
        visible,
        onVisibleChange: setVisible,
      }"
      :src="previewUrl || src"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { EyeOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import ErrorIcon from '@/assets/icons/error-image.svg'
import { getPreviewUrl } from '@/utils/index'
import { useIntersectionObserver } from '@/hook/useIntersectionObserver'

const props = withDefaults(
  defineProps<{
    src: string | null
    height?: number | string
    width?: number
    id?: number | null
  }>(),
  {
    src: '',
    height: '100%',
  },
)

const visible = ref<boolean>(false)
const setVisible = (value): void => {
  visible.value = value
}

const loading = ref(false)
const previewUrl = ref()
const imageContainerRef = ref<HTMLElement>()

const autoStyle = computed(() => {
  const style: any = {}
  if (props.width) style.width = `${props.width}px`
  if (props.height) style.height = `${props.height}px`
  return style
})

// 使用可视区域检测 hook
const { isIntersecting, setElement } = useIntersectionObserver({
  threshold: 0.1,
  rootMargin: '50px',
})

// 监听容器元素变化
watch(imageContainerRef, (el) => {
  setElement(el || null)
})

// 获取附件链接
const getFileUrl = async () => {
  if (!props.id) {
    previewUrl.value = null
    return
  }

  try {
    loading.value = true
    const newUrl = await getPreviewUrl(props.id)
    previewUrl.value = newUrl
  } catch (error) {
    console.error('获取图片预览失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听可视区域变化
watch(isIntersecting, (intersecting) => {
  if (intersecting && props.id && !previewUrl.value && !loading.value) {
    getFileUrl()
  }
})

watch(
  () => props.id,
  (newId) => {
    if (newId) {
      getFileUrl()
    } else {
      previewUrl.value = null
      loading.value = false
    }
  },
)
</script>

<style lang="scss" scoped>
.base-image-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  z-index: 1;
  cursor: pointer;
  transition: opacity 0.3s ease-in-out;
  &:hover {
    opacity: 1;
  }
}
</style>
