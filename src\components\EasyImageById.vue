<template>
  <EasyImage
    v-if="imageUrl || !fileId"
    :src="imageUrl || (fileId ? null : '/src/assets/icons/error-image.svg')"
    :w="w"
    :h="h"
    :maskClassName="maskClassName"
    :hidePreview="hidePreview"
    :imgStyle="imgStyle"
  />
  <div v-else class="flex items-center justify-center" :style="{ width: w + 'px', height: h + 'px', backgroundColor: '#f5f5f5', border: '1px solid #d9d9d9', borderRadius: '4px' }">
    <img src="/src/assets/icons/error-image.svg" alt="默认图片" :style="{ width: w + 'px', height: h + 'px', objectFit: 'cover', borderRadius: '4px' }" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted } from 'vue'
import { generateImageUrlFromFileId } from '@/utils/index'
import EasyImage from './EasyImage.vue'

const props = withDefaults(
  defineProps<{
    fileId: number | null
    w?: number
    h?: number
    maskClassName?: string
    hidePreview?: boolean
    imgStyle?: any
  }>(),
  {
    w: 28,
    h: 28,
    maskClassName: '预览',
    fileId: null,
    hidePreview: false,
    imgStyle: () => {},
  },
)

const imageUrl = ref<string | null>(null)
const loading = ref(false)

// 图片URL缓存 - 使用全局缓存避免重复请求
const imageUrlCache = new Map<number, string>()

const loadImageUrl = async (fileId: number | null) => {
  if (!fileId) {
    imageUrl.value = null
    return
  }

  // 检查缓存
  if (imageUrlCache.has(fileId)) {
    imageUrl.value = imageUrlCache.get(fileId) || null
    return
  }

  try {
    loading.value = true
    const url = await generateImageUrlFromFileId(fileId)
    if (url) {
      imageUrlCache.set(fileId, url)
      imageUrl.value = url
    } else {
      imageUrl.value = null
    }
  } catch (error) {
    console.error('获取图片URL失败:', error)
    imageUrl.value = null
  } finally {
    loading.value = false
  }
}

// 监听fileId变化
watch(
  () => props.fileId,
  (newFileId) => {
    loadImageUrl(newFileId)
  },
  { immediate: true },
)

// 组件卸载时清理资源
onUnmounted(() => {
  if (imageUrl.value) {
    URL.revokeObjectURL(imageUrl.value)
  }
})
</script>
