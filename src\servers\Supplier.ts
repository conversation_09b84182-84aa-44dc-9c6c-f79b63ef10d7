import { request } from './request'

// 通过用户id获取供应商信息
export const GetSupplierInfo = () => {
  return request({ url: '/api/Supplier/GetSupplier' }, 'GET', null, false)
}

// 添加修改基础供应商信息
export const saveSupplierSettlement = (data: any) => {
  return request({ url: '/api/Supplier/AddorUpdateSupplierBasic', data }, 'POST')
}

// 保存供应商拓展信息
export const saveSupplierExpand = (data: any) => {
  return request({ url: '/api/Supplier/UpdateSupplierExpand', data }, 'POST')
}

// 保存供应商跟进信息
export const saveSupplierFollow = (data: any) => {
  return request({ url: '/api/Supplier/UpdateSupplierFollow', data }, 'POST')
}

// 添加证书
export const updateSupplierLicenseFileIds = (data: any) => {
  return request({ url: '/api/Supplier/UpdateSupplierLicenseFileIds', data }, 'POST')
}

// 获取供应商下拉列表数据
export const GetSupplierDropdownData = () => {
  return request({ url: '/api/Supplier/GetSupplierDropdownData' }, 'GET')
}
