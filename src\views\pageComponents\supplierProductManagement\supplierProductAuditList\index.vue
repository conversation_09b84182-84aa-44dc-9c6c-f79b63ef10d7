<template>
  <div class="main">
    <div>
      <a-tabs class="filterTabs w-full" v-model:active-key="otherParams.status" @change="handleFilterScopeChange">
        <a-tab-pane v-for="item in statusTabs" :key="item.value">
          <template #tab>
            <a-badge v-if="btnPermission[item.permission]" :count="item.total_count" :offset="[12, -2]" :overflowCount="999">
              <span>{{ item.label }}</span>
            </a-badge>
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>
    <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.PRODUCT_AUDIT" @search="search" @setting="tableRef?.showTableSetting()"></SearchForm>
    <BaseTable ref="tableRef" :isCheckbox="true" :pageType="PageType.PRODUCT_AUDIT" :get-list="getListFn" v-model:form="formArr" @init-finish="handleInitFinish" :isIndex="true">
      <template #right-btn>
        <a-select v-model:value="exportSelectValue" class="w-120px" :disabled="exportBtnPermission">
          <a-select-option v-for="item in exportSelect" :key="item.value" :value="item.value" :label="item.label">
            {{ item.label }}
          </a-select-option>
        </a-select>
        <a-button @click="handleExport" class="btn" :disabled="exportBtnPermission">导出</a-button>
      </template>
      <template #shipment_time="{ row }">
        <span>{{ row.shipment_time?.slice(0, 10) }}</span>
      </template>
      <template #expected_delivery_date="{ row }">
        <span>{{ row.expected_delivery_date?.slice(0, 10) }}</span>
      </template>
      <template #supplier_product_number="{ row }">
        <copy-btn v-copy="row.supplier_product_number" v-if="row.supplier_product_number" />
        <span>{{ row.supplier_product_number }}</span>
      </template>
      <template #product_number="{ row }">
        <copy-btn v-copy="row.product_number" v-if="row.product_number" />
        <span>{{ row.product_number }}</span>
      </template>
      <template #supplier_number="{ row }">
        <copy-btn v-copy="row.supplier_number" v-if="row.supplier_number" />
        <span>{{ row.supplier_number }}</span>
      </template>
      <template #operate="{ row }">
        <a-space>
          <a-button type="text" @click="handleView(row.id)" :disabled="detailBtnPermission">查看</a-button>
          <a-button v-if="otherParams.status == 10 && btnPermission[92202]" type="text" @click="onOpenAuditModal(row.id, 'pass')" :disabled="detailBtnPermission">通过</a-button>
          <a-button v-if="otherParams.status == 10 && btnPermission[92203]" type="text" @click="onOpenAuditModal(row.id, 'overrule')" :disabled="detailBtnPermission">
            <span class="c-red">驳回</span>
          </a-button>
          <a-button v-if="otherParams.status == 10 && btnPermission[92204]" type="text" @click="onOpenAuditModal(row.id, 'refuse')" :disabled="detailBtnPermission">
            <span class="c-red">拒绝选品</span>
          </a-button>
        </a-space>
      </template>
      <template #operate_header="{ item }">
        <a-space>
          <span>{{ item.name }}</span>
          <a-tooltip v-if="otherParams.status == 10 && (btnPermission[92202] || btnPermission[92203] || btnPermission[92204])" placement="topLeft" :overlayInnerStyle="{ width: '300px' }">
            <template #title>
              <div>通过：同意审核内容，可以 “通过” 审核。</div>
              <div>驳回：若内容不符合要求，审核人可驳回并反馈修改意见，提审人调整后可重新提交审核。</div>
              <div>拒绝选品：若商品不符合选品标准，审核人可拒绝，该商品将无法再次提交。</div>
            </template>
            <QuestionCircleOutlined />
          </a-tooltip>
        </a-space>
      </template>
    </BaseTable>
    <ProductViewDrawer v-if="visible" v-model:visible="visible" :id="selectedId" @operateSuccess="onOperateSuccess" />
    <AuditModal ref="auditRef" @success="onOperateSuccess"></AuditModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import { PageType } from '@/common/enum'
import BaseTable from '@/components/BaseTable/index.vue'
import { GetCategoryOption } from '@/servers/SupplierProduceStock'
import { GetProductApprovalList } from '@/servers/supplierProductAuditList'
import SearchForm from '@/components/SearchForm/index.vue'
import { getCommonOption } from '@/utils'
import { Add as AddDownloadTask } from '@/servers/DownloadCenter'
import { usePermission } from '@/hook/usePermission'
import CopyBtn from '@/components/CopyBtn/index.vue'
import eventBus from '@/utils/eventBus'
import useBadgeStore from '@/store/modules/badgeStore'
import ProductViewDrawer from './components/ProductViewDrawer.vue'
import AuditModal from './components/AuditModal.vue'
import { formArrAll, formArrAllWaitAudit, formArrAllAudited } from './config/formArr'

const badgeStore = useBadgeStore()
const { btnPermission } = usePermission()

const visible = ref(false)
const selectedId = ref()

const tableKey = ref([])
const auditRef = ref()

const statusTabs = ref([
  {
    label: '全部',
    value: 0,
    permission: 92100,
    detailPermission: 92101,
    exportPermission: 92102,
    exportType: 21,
  },
  {
    label: '待审核',
    value: 10,
    total_count: 0,
    permission: 92200,
    detailPermission: 92201,
    exportPermission: 92202,
    exportType: 22,
  },
  {
    label: '审核通过',
    value: 20,
    permission: 92300,
    detailPermission: 92301,
    exportPermission: 92302,
    exportType: 23,
  },
  {
    label: '已驳回',
    value: 30,
    permission: 92400,
    detailPermission: 92401,
    exportPermission: 92402,
    exportType: 24,
  },
  {
    label: '已拒绝',
    value: 40,
    permission: 92500,
    detailPermission: 92501,
    exportPermission: 92502,
    exportType: 25,
  },
])

const detailBtnPermission = computed(() => {
  return !btnPermission.value[statusTabs.value.find((n) => n.value == otherParams.value.status)?.detailPermission || 0]
})

const exportBtnPermission = computed(() => {
  return !btnPermission.value[statusTabs.value.find((n) => n.value == otherParams.value.status)?.exportPermission || 0]
})

// 搜索功能
const search = () => tableRef.value.search()

const lineHeightType = computed(() => {
  return tableRef.value.lineHeightType
})

const getListFn = (obj: any) => {
  if (!formArr.value.find((item: any) => item.key === 'brand_id_list').value) {
    formArr.value.find((item: any) => item.key === 'brand_id_list').value = []
  }
  const params = {
    ...obj,
    is_page: true,
    is_get_total: true,
    is_get_total_only: false,
    is_audit_data: true,
  }
  if (formArr.value.find((item: any) => item.key === 'status')?.value) {
    params.status = formArr.value.find((item: any) => item.key === 'status')?.value || ''
  } else {
    params.status = otherParams.value.status || ''
  }

  // 处理是否白牌参数
  const isWhiteBrandItem = formArr.value.find((item: any) => item.key === 'is_white_brand')
  if (isWhiteBrandItem && isWhiteBrandItem.value !== null && isWhiteBrandItem.value !== undefined) {
    params.is_white_brand = isWhiteBrandItem.value
    console.log('🔍 [getListFn] 添加是否白牌参数:', params.is_white_brand)
  }

  // 处理商品类目参数，将级联选择器的数组数组转换为叶子节点ID数组
  if (params.category_id_list && Array.isArray(params.category_id_list)) {
    const leafIds: number[] = []
    params.category_id_list.forEach((path: number[]) => {
      if (path && path.length > 0) {
        // 取每个路径的最后一个ID（叶子节点）
        leafIds.push(path[path.length - 1])
      }
    })
    params.category_id_list = leafIds
  }
  return GetProductApprovalList(params)
}

// 商品类目下拉数据
const categoryOptions = ref<any[]>([])

const otherParams = ref({
  status: 0,
})

const exportSelectValue = ref(1)
// 导出下拉框
const exportSelect = ref([
  {
    label: '导出全部',
    value: 1,
  },
  {
    label: '导出所选',
    value: 2,
  },
  {
    label: '导出筛选条件',
    value: 3,
  },
])

// // 许可证 资质证书ref
// const licenseRef = ref<any>()
// 表格和表单引用
const tableRef = ref()
// 搜索表单配置
const formArr: any = ref(formArrAll)
const handleView = (id: number) => {
  console.log(11)

  selectedId.value = id
  visible.value = true
}

// // 许可证和资质弹窗
// const handleViewLicense = (id: number, type: string) => {
//   licenseRef.value.showDrawer(id, type)
// }

// 处理是否白牌筛选项的联动逻辑
const handleWhiteBrandChange = (value: boolean | null) => {
  // 开始处理白牌筛选联动
  // console.log('🔍 [handleWhiteBrandChange] 开始处理白牌筛选联动:', { value })

  const brandFilterItem = formArr.value.find((item: any) => item.key === 'brand_id_list')

  if (!brandFilterItem) {
    console.warn('🔍 [handleWhiteBrandChange] 未找到品牌筛选项')
    return
  }

  if (value === true) {
    // 选择"是"（白牌）时：清空品牌筛选并禁用
    console.log('🔍 [handleWhiteBrandChange] 选择白牌，禁用品牌筛选')
    if (brandFilterItem.value && brandFilterItem.value.length > 0) {
      brandFilterItem._previousValue = [...brandFilterItem.value]
      console.log('🔍 [handleWhiteBrandChange] 保存之前的品牌选择:', brandFilterItem._previousValue)
    }
    brandFilterItem.value = []
    brandFilterItem.disabled = true
  } else if (value === false) {
    // 选择"否"（非白牌）时：重新启用品牌筛选
    console.log('🔍 [handleWhiteBrandChange] 选择非白牌，启用品牌筛选')
    brandFilterItem.disabled = false
  } else {
    // 清空选择时：重新启用品牌筛选
    console.log('🔍 [handleWhiteBrandChange] 清空选择，启用品牌筛选')
    brandFilterItem.disabled = false
    if (brandFilterItem._previousValue) {
      delete brandFilterItem._previousValue
    }
  }

  console.log('🔍 [handleWhiteBrandChange] 联动处理完成:', {
    brandDisabled: brandFilterItem.disabled,
    brandValue: brandFilterItem.value,
  })
}

const handleFilterScopeChange = () => {
  // 全部
  switch (otherParams.value.status) {
    case 0:
      formArr.value = JSON.parse(JSON.stringify(formArrAll))
      break
    case 10:
      formArr.value = JSON.parse(JSON.stringify(formArrAllWaitAudit))
      break
    case 20:
    case 30:
    case 40:
      formArr.value = JSON.parse(JSON.stringify(formArrAllAudited))
      break
    default:
      break
  }
  if (otherParams.value.status === 0) {
    tableRef.value.tableKey = tableKey.value
    if (formArr.value.find((item: any) => item.key === 'status')) {
      formArr.value.find((item: any) => item.key === 'status').isShow = formArrAll.find((item: any) => item.key === 'status')!.isShow // true
    }
    if (formArr.value.find((item: any) => item.key === 'audit_time')) {
      formArr.value.find((item: any) => item.key === 'audit_time').isShow = formArrAll.find((item: any) => item.key === 'audit_time')!.isShow // true
    }
  } else if (otherParams.value.status === 10) {
    const delKeys = ['submit_audit_name', 'status_string', 'audit_name', 'audit_time', 'audit_notes']
    tableRef.value.tableKey = tableKey.value.filter((item: any) => !delKeys.includes(item.key))
    if (formArr.value.find((item: any) => item.key === 'status')) {
      formArr.value.find((item: any) => item.key === 'status').isShow = formArrAllWaitAudit.find((item: any) => item.key === 'status')!.isShow // false
    }
    if (formArr.value.find((item: any) => item.key === 'audit_time')) {
      formArr.value.find((item: any) => item.key === 'audit_time').isShow = formArrAllWaitAudit.find((item: any) => item.key === 'audit_time')!.isShow // false
    }
  } else {
    tableRef.value.tableKey = tableKey.value.filter((item: any) => item.key !== 'status_string')
    if (formArr.value.find((item: any) => item.key === 'status')) {
      formArr.value.find((item: any) => item.key === 'status').isShow = formArrAllAudited.find((item: any) => item.key === 'status')!.isShow // false
    }
  }

  // 设置是否白牌筛选项的联动逻辑
  console.log('🔍 [handleFilterScopeChange] 设置白牌筛选联动逻辑')
  const whiteBrandItem = formArr.value.find((item: any) => item.key === 'is_white_brand')
  if (whiteBrandItem) {
    whiteBrandItem.onChange = handleWhiteBrandChange
    console.log('🔍 [handleFilterScopeChange] 白牌筛选联动逻辑已设置')

    // 如果当前已选择白牌，需要立即应用联动逻辑
    if (whiteBrandItem.value === true) {
      console.log('🔍 [handleFilterScopeChange] 当前已选择白牌，应用联动逻辑')
      handleWhiteBrandChange(true)
    }
  }

  setTimeout(() => {
    tableRef.value.tableRef.refreshColumn()
  }, 500)
  search()

  getCertificateType()
}

const handleInitFinish = (val) => {
  tableKey.value = val
}
const getCertificateType = async () => {
  const [productBrandOptions, statusOptions, submitAuditOptions] = await getCommonOption([19, 22, 24])
  formArr.value.forEach((item) => {
    if (item.key === 'brand_id_list') {
      // 品牌下拉数据
      item.options = productBrandOptions
    }
    if (item.key == 'status') {
      item.options = statusOptions
    }
    if (item.key == 'submit_audit_id') {
      item.options = submitAuditOptions
    }
  })

  // 设置是否白牌筛选项的联动逻辑
  const whiteBrandItem = formArr.value.find((item: any) => item.key === 'is_white_brand')
  if (whiteBrandItem) {
    whiteBrandItem.onChange = handleWhiteBrandChange

    // 如果当前已选择白牌，需要立即应用联动逻辑
    if (whiteBrandItem.value === true) {
      handleWhiteBrandChange(true)
    }
  }
}
const getCategoryOption = async () => {
  const res = await GetCategoryOption()
  categoryOptions.value = res.data
  const formatTreeData = (categoryOptions) => {
    return categoryOptions.map((item) => {
      const node = {
        label: item.name,
        value: item.id,
        children: item.children?.length ? formatTreeData(item.children) : undefined,
      }
      return node
    })
  }
  const treeData = formatTreeData(res.data)
  formArr.value.forEach((item) => {
    if (item.key === 'category_id_list') {
      item.options = treeData
    }
  })
}

// 导出
const handleExport = async () => {
  const ids = tableRef.value.checkItemsArr.map((item: any) => item.id)
  const createTime = formArr.value.find((item: any) => item.key === 'create_at')?.value
  const modifiedTime = formArr.value.find((item: any) => item.key === 'modified_at')?.value
  const submitAuditTime = formArr.value.find((item: any) => item.key === 'submit_audit_at')?.value
  const auditTime = formArr.value.find((item: any) => item.key === 'audit_time')?.value
  // 构建filter对象
  const filter: any = {
    supplier_product_number: formArr.value.find((item: any) => item.key === 'supplier_product_number')?.value,
    product_number: formArr.value.find((item: any) => item.key === 'product_number')?.value,
    product_name: formArr.value.find((item: any) => item.key === 'product_name')?.value,
    product_name_en: formArr.value.find((item: any) => item.key === 'product_name_en')?.value,
    is_white_brand: formArr.value.find((item: any) => item.key === 'is_white_brand')?.value,
    brand_id_list: formArr.value.find((item: any) => item.key === 'brand_id_list')?.value,
    category_id_list: formArr.value.find((item: any) => item.key === 'category_id_list')?.value,
    min_declared_purchase_price: formArr.value.find((item: any) => item.key === 'declared_purchase_price')?.minValue,
    max_declared_purchase_price: formArr.value.find((item: any) => item.key === 'declared_purchase_price')?.maxValue,
    min_agreed_purchase_price: formArr.value.find((item: any) => item.key === 'agreed_purchase_price')?.minValue,
    max_agreed_purchase_price: formArr.value.find((item: any) => item.key === 'agreed_purchase_price')?.maxValue,
    min_store_supply_price: formArr.value.find((item: any) => item.key === 'store_supply_price')?.minValue,
    max_store_supply_price: formArr.value.find((item: any) => item.key === 'store_supply_price')?.maxValue,
    min_first_batch_quantity: formArr.value.find((item: any) => item.key === 'first_batch_quantity')?.minValue,
    max_first_batch_quantity: formArr.value.find((item: any) => item.key === 'first_batch_quantity')?.maxValue,
    min_shipping_fee: formArr.value.find((item: any) => item.key === 'shipping_fee')?.minValue,
    max_shipping_fee: formArr.value.find((item: any) => item.key === 'shipping_fee')?.maxValue,
    min_shipment_time: formArr.value.find((item: any) => item.key === 'shipment_time')?.minValue,
    max_shipment_time: formArr.value.find((item: any) => item.key === 'shipment_time')?.maxValue,
    min_expected_delivery_date: formArr.value.find((item: any) => item.key === 'expected_delivery_date')?.minValue,
    max_expected_delivery_date: formArr.value.find((item: any) => item.key === 'expected_delivery_date')?.maxValue,
    submit_audit_id: formArr.value.find((item: any) => item.key === 'submit_audit_id')?.value,
    min_submit_audit_at: Array.isArray(submitAuditTime) && submitAuditTime[0] ? new Date(submitAuditTime[0]).toISOString() : undefined,
    max_submit_audit_at: Array.isArray(submitAuditTime) && submitAuditTime[1] ? new Date(submitAuditTime[1]).toISOString() : undefined,
    status: formArr.value.find((item: any) => item.key === 'status')?.value,
    supplier_number: formArr.value.find((item: any) => item.key === 'supplier_number')?.value,
    supplier_name: formArr.value.find((item: any) => item.key === 'supplier_name')?.value,
    min_audit_time: Array.isArray(auditTime) && auditTime[0] ? new Date(auditTime[0]).toISOString() : undefined,
    max_audit_time: Array.isArray(auditTime) && auditTime[1] ? new Date(auditTime[1]).toISOString() : undefined,
    create_at_start: Array.isArray(createTime) && createTime[0] ? new Date(createTime[0]).toISOString() : undefined,
    create_at_end: Array.isArray(createTime) && createTime[1] ? new Date(createTime[1]).toISOString() : undefined,
    modified_at_start: Array.isArray(modifiedTime) && modifiedTime[0] ? new Date(modifiedTime[0]).toISOString() : undefined,
    modified_at_end: Array.isArray(modifiedTime) && modifiedTime[1] ? new Date(modifiedTime[1]).toISOString() : undefined,
  }

  // 处理商品类目参数，将级联选择器的数组数组转换为叶子节点ID数组
  if (filter.category_id_list && Array.isArray(filter.category_id_list)) {
    const leafIds: number[] = []
    filter.category_id_list.forEach((path: number[]) => {
      if (path && path.length > 0) {
        // 取每个路径的最后一个ID（叶子节点）
        leafIds.push(path[path.length - 1])
      }
    })
    filter.category_id_list = leafIds
  }

  const params = {
    page_export_type: statusTabs.value.find((n) => n.value == otherParams.value.status)?.exportType,
    export_type: exportSelectValue.value,
    ids,
    filter,
  }
  if (exportSelectValue.value == 1) {
    delete (params as any).ids
    delete (params as any).filter
    if (otherParams.value.status != 0) {
      ;(params as any).filter = {
        status: formArr.value.find((item: any) => item.key === 'status')?.value || otherParams.value.status,
      }
    }
  }
  if (exportSelectValue.value == 2) {
    delete (params as any).filter
    if (ids.length == 0) {
      message.warning('请勾选要导出的商品')
      return
    }
  }
  if (exportSelectValue.value == 3) {
    delete (params as any).ids
  }

  try {
    // 使用新的下载中心接口
    const downloadParams = {
      file_name: '商品导出',
      export_type_identifier: '5',
      export_params: params as any,
    }

    const res = await AddDownloadTask(downloadParams)
    if (res.success) {
      message.success('导出任务已添加到下载队列，请到下载中心查看进度')
    } else {
      message.error(res.message || '添加导出任务失败')
    }
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  }
}

const getRedNumber = async () => {
  badgeStore.fetchBadgeCounts().then(() => {
    statusTabs.value[1].total_count = badgeStore.badgeCounts?.managementAuditLabelStatusCount?.waiting_count
  })
}

const onOpenAuditModal = (id, type: string) => {
  auditRef.value.showModal(id, type)
}

const onOperateSuccess = () => {
  getRedNumber()

  // 操作完成后刷新当前列表(页码不变)
  tableRef.value.maintainSearch()

  // 通知主菜单更新商品审核红点
  eventBus.emit('updateProductAuditRedPoint')
}

onMounted(async () => {
  getCategoryOption()
  getCertificateType()
  getRedNumber()
})
</script>

<style lang="scss" scoped>
.image {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

// 给小红点（badge count）添加最小宽度30px，与供应商商品库保持一致
:deep(.ant-badge .ant-badge-count.ant-scroll-number) {
  max-width: 30px;
  padding: 0 4px;
}

:deep(.ant-badge-count) {
  z-index: 10 !important;
}
</style>
