<template>
  <a-drawer :width="1000" title="订单详情" :open="visible" @close="handleClose">
    <template #extra>
      <a-space>
        <a-button :loading="loading" :disabled="currentIdx === 0" @click="handleChangeInfo('prev')">上一条</a-button>
        <a-button :loading="loading" :disabled="currentIdx === tableIds.length - 1" @click="handleChangeInfo('next')">下一条</a-button>
      </a-space>
    </template>
    <a-spin :spinning="loading">
      <div v-if="details.order_status === 128" class="pl-12 pt-20 pb-20 color-#FF8D1A bg-#FCF6EC mb-20 text-20 font-600">订单已取消</div>
      <div v-else-if="details.order_status === 256" class="pl-12 pt-20 pb-20 bg-#FEF0F0 mb-20">
        <div class="color-#EB1237 text-20 font-600 mb-6">订单异常</div>
        <div class="color-#666">异常原因：{{ details?.question_type }}</div>
      </div>
      <div v-else class="px-10% mb-16px">
        <a-steps :current="selectStep" :items="stepList" size="small"></a-steps>
      </div>
      <Form />
      <div class="drawer-title">订单商品</div>
      <vxe-table
        border
        size="small"
        :data="details.order_items"
        class="!text-12"
        ref="tableRef"
        :merge-cells="mergeCells"
        :column-config="{
          resizable: true,
        }"
        :row-config="{ isHover: true, height: 72 }"
      >
        <vxe-column type="seq" width="50" align="center"></vxe-column>
        <vxe-column min-width="85" field="name" title="主图">
          <template #default="{ row }">
            <BaseImage :width="60" :height="60" :id="row.main_images_id"></BaseImage>
          </template>
        </vxe-column>
        <vxe-column width="150" field="product_name" title="商品名称" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="product_number" title="平台商品编码" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="spec_info" title="规格型号" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="supplier" title="所属供应商" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="qty" title="订单数量" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="declared_purchase_tax_price" title="采购单价(含税)" :formatter="priceFormatter"></vxe-column>
        <vxe-column width="150" field="total_amount" title="合计金额" :formatter="priceFormatter"></vxe-column>
        <vxe-column width="150" field="inventory_num" title="可用库存" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="remark" title="备注" :formatter="defaultFormatter"></vxe-column>
      </vxe-table>
      <div class="flex justify-end mt-20 mb-20">
        <div class="c-#999 w-200 flex flex-col gap-4 lh-20px">
          <div class="flex justify-between">
            <span>商品总数量:</span>
            <span>{{ countTotal }}</span>
          </div>
          <div class="flex justify-between text-12px c-#333 font-600">
            <span>订单总金额：</span>
            <span>¥{{ new Decimal(details?.total_amount).toFixed(2) }}</span>
          </div>
        </div>
      </div>
    </a-spin>
  </a-drawer>
  <a-modal v-model:open="openModal" title="附件" :footer="null">
    <div class="min-h-[250px]">
      <div v-for="(item, i) in filesList" :key="i" class="cursor-pointer">
        <a-space class="cursor-point items-start" @click="previewAuthFile(item)">
          <LinkOutlined />
          <span class="text-blue-500 font-size-14 break-all">{{ item }}</span>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { useBaseForm, type BaseFormItem } from '@/hook/useBaseForm'
import { GetSaleOrderDetail } from '@/servers/OrderManage'
import { message } from 'ant-design-vue'
import Decimal from 'decimal.js'
import { withDirectives } from 'vue'
import { vCopy } from '@/directive/copy'
import { defaultFormatter, priceFormatter } from '@/utils/VxeUi'
import { VxeTable, VxeTablePropTypes } from 'vxe-table'
import BaseImage from '@/components/BaseImage/index.vue'
import { CopyOutlined, LinkOutlined } from '@ant-design/icons-vue'
// import OrderPurchaseItem from './OrderPurchaseItem.vue'

const VITE_APP_ENV = ref<string | undefined>(import.meta.env.VITE_APP_ENV)
const userData = ref<Record<string, any>>(JSON.parse(localStorage.getItem('userData') || '{}'))

const currentIdx = computed(() => tableIds.value.findIndex((id) => id === selectId.value))

const visible = ref(false)
// 加载状态
const loading = ref(false)
// 当前选中的id
const selectId = ref()
// 合并单元格
const mergeCells = ref<VxeTablePropTypes.MergeCells>([])
const tableRef = useTemplateRef<InstanceType<typeof VxeTable>>('tableRef')
const tableIds = ref<number[]>([])
const openModal = ref(false)
// 查看附件弹窗中的列表
const filesList = ref<string[]>([])
// 表单
const details = ref<any>({
  order_items: [],
  total_amount: 0,
  files: [
    {
      id: 123,
      name: '测试',
    },
  ],
})
// 当前步骤条
const selectStep = ref(0)
// 步骤条列表
const stepList = [
  {
    title: '待付款',
    order_status: 1,
  },
  {
    title: '已付款待审核',
    order_status: 2,
  },
  {
    title: '待财审',
    order_status: 4,
  },
  {
    title: '发货中',
    order_status: 8,
  },
  {
    title: '已发货',
    order_status: 16,
  },
  {
    title: '已签收',
    order_status: 32,
  },
  {
    title: '已完成',
    order_status: 64,
  },
]

// 计算商品总数量
const countTotal = computed(() => {
  return new Decimal(details.value.order_items.reduce((acc, curr) => acc + (curr.qty || 0), 0)).toNumber()
})

// 获取详情
const getDetail = (id: string) => {
  const params = { id }
  loading.value = true
  GetSaleOrderDetail(params)
    .then((res) => {
      details.value = res.data
      if (![128, 256].includes(res.data.order_status)) {
        selectStep.value = stepList.findIndex((item) => item.order_status === res.data.order_status)
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 合并
const mergeTableCells = (data: any) => {
  const columns = tableRef.value?.getColumns() || []
  const mergeList: any[] = []
  mergeCells.value = []
  let findIndex = 0
  const list = data.flatMap((item: any, index: number) => {
    if (item.purchase_in_relation_infos?.length === 0) {
      return item
    }
    if (item.purchase_in_relation_infos?.length > 1) {
      // 为每个需要合并的列创建合并规则
      columns.forEach((col, colIndex) => {
        const mergeKeys = ['io_id', 'purchase_inbound_quantity', 'inbound_time']
        if (!mergeKeys.includes(col.field)) {
          mergeList.push({
            row: index + findIndex,
            col: colIndex,
            rowspan: item.purchase_in_relation_infos.length,
            colspan: 1,
          })
        }
      })
      findIndex += item.purchase_in_relation_infos.length - 1
    }
    return item.purchase_in_relation_infos.map((product: any) => {
      return {
        ...item,
        ...product,
        id: product.io_id,
      }
    })
  })
  nextTick(() => {
    mergeCells.value = mergeList
  })
  return list
}

// 关闭
const handleClose = () => {
  visible.value = false
}
// 显示
const handleShow = (id: string, ids: string[]) => {
  selectId.value = id
  visible.value = true
  tableIds.value = ids
  getDetail(id)
}

// 上一条
const handleChangeInfo = (type: 'prev' | 'next') => {
  const index = tableIds.value.findIndex((id) => id === selectId.value)
  if (index === 0 && type === 'prev') {
    message.error('已经是第一条')
    return
  }
  if (index === tableIds.value.length - 1 && type === 'next') {
    message.error('已经是最后一条')
    return
  }

  if (type === 'prev') {
    selectId.value = tableIds.value[index - 1]
  } else {
    selectId.value = tableIds.value[index + 1]
  }
  getDetail(selectId.value)
}

const formArr = ref<BaseFormItem[]>([
  {
    type: 'title',
    label: '交易信息',
  },
  {
    label: () =>
      h('div', {}, [h('span', {}, '聚水潭订单编号:'), details.value.jst_o_id && withDirectives(h(CopyOutlined, { class: 'ml-4 c-primary cursor-pointer' }), [[vCopy, details.value.jst_o_id]])]),
    key: 'jst_o_id',
    type: 'text',
    span: 6,
  },
  {
    label: () =>
      h('div', {}, [h('span', {}, '线上订单编号:'), details.value.jst_so_id && withDirectives(h(CopyOutlined, { class: 'ml-4 c-primary cursor-pointer' }), [[vCopy, details.value.jst_so_id]])]),
    key: 'jst_so_id',
    type: 'text',
    span: 6,
  },
  {
    label: '下单时间:',
    key: 'order_create_at',
    type: 'text',
    span: 6,
  },
  {
    label: '付款时间:',
    key: 'pay_date',
    type: 'text',
    span: 6,
  },
  {
    label: '完成时间:',
    key: 'finish_date',
    type: 'text',
    span: 6,
  },
  {
    label: '买家留言:',
    key: 'buyer_message',
    type: 'text',
    span: 6,
  },
  {
    label: '卖家备注:',
    key: 'remark',
    type: 'text',
    span: 6,
  },
  {
    label: '收货信息',
    type: 'title',
  },
  {
    label: '收货人:',
    key: 'recipient',
    type: 'text',
    span: 6,
  },
  {
    label: '联系电话:',
    key: 'telephone',
    type: 'text',
    span: 6,
  },
  {
    label: '收货地址:',
    key: 'address_info',
    type: 'text',
    span: 6,
  },
  {
    label: '邮编:',
    key: 'zipcode',
    type: 'text',
    span: 6,
  },
  {
    label: '配送信息',
    type: 'title',
  },
  {
    label: '快递公司:',
    key: 'logistics_company',
    type: 'text',
    span: 6,
  },
  {
    label: '快递单号:',
    key: 'tracking_number',
    type: 'text',
    span: 6,
  },
  {
    label: '箱唛（面单）:',
    key: 'shipping_mark',
    type: 'slot',
    span: 6,
    slots: () => {
      if (details.value?.shipping_mark?.length) {
        return h('div', { class: 'c-primary cursor-pointer', onClick: () => onClickFiles('shipping_mark') }, '查看附件')
      }
      return h('div', {}, '--')
    },
  },
  {
    label: '条形码（标签）:',
    key: 'shipping_barcode',
    type: 'slot',
    span: 6,
    slots: () => {
      if (details.value?.shipping_barcode?.length) {
        return h('div', { class: 'c-primary cursor-pointer', onClick: () => onClickFiles('shipping_barcode') }, '查看附件')
      }
      return h('div', {}, '--')
    },
  },
  {
    label: '其他附件:',
    key: 'other_files',
    type: 'slot',
    span: 6,
    slots: () => {
      if (details.value?.other_files?.length) {
        return h('div', { class: 'c-primary cursor-pointer', onClick: () => onClickFiles('other_files') }, '查看附件')
      }
      return h('div', {}, '--')
    },
  },
  {
    label: '发货区域:',
    key: 'delivery_region_name',
    type: 'text',
    span: 6,
  },
  {
    label: '发货仓库:',
    key: 'depot_name',
    type: 'text',
    span: 6,
  },
  {
    label: '交接单:',
    key: 'delivery_receipt',
    type: 'slot',
    span: 6,
    slots: () => {
      if (details.value?.delivery_receipt?.length) {
        return h('div', { class: 'c-primary cursor-pointer', onClick: () => onClickFiles('delivery_receipt') }, '查看附件')
      }
      return h('div', {}, '--')
    },
  },
])

const onClickFiles = (key: string) => {
  openModal.value = true
  filesList.value = details.value[key]
}

const previewAuthFile = async (files: string) => {
  // const fileId = files.id
  // if (!fileId) {
  //   message.warning('文件不存在')
  //   return
  // }
  try {
    // let url = ''
    // if (VITE_APP_ENV.value == 'development') {
    //   url = `${window.location.origin}/api/api/Files/ViewByFileId?fileId=${fileId}`
    // } else {
    //   url = `${window.location.origin}/api/Files/ViewByFileId?fileId=${fileId}`
    // }

    // const response = await fetch(url, {
    //   method: 'GET',
    //   headers: {
    //     'Content-Type': 'application/octet-stream',
    //     logintoken: (userData.value as any).login_token,
    //   },
    // })
    // if (!response.ok) {
    //   message.warning('获取授权书失败')
    //   throw new Error(`HTTP error! status: ${response.status}`)
    // }

    // const blob = await response.blob()
    // const previewUrl = URL.createObjectURL(blob)
    window.open(files, '_blank')
    setTimeout(() => URL.revokeObjectURL(files), 30000)
  } catch (error) {
    console.error('授权书预览失败:', error)
    // message.error('授权书预览失败')
  }
}

const [Form] = useBaseForm({
  formConfig: formArr,
  modelValue: details,
  isText: true,
})

defineExpose({
  show: handleShow,
})
</script>

<style scoped lang="scss">
:deep(.drawer-title) {
  position: relative;

  &::after {
    position: absolute;
    top: 50%;
    left: 0;
    width: 2px;
    height: 12px;
    content: '';
    background-color: #1890ff;
    transform: translateY(-50%);
  }
}

:deep(.ant-form) {
  color: red !important;

  .ant-form-item {
    .ant-form-item-label {
      label {
        color: #999 !important;
      }
    }

    .ant-form-item-control {
      .a-text-wrap {
        color: #333;
      }
    }
  }
}
</style>
