import VxeUI from 'vxe-pc-ui'

import 'vxe-pc-ui/lib/style.css'
import '@/assets/style/vxe-reset.scss'
import { imageListRender, textRender, imageRender, copyRender } from '@/utils/VxeRender'
import { formatMap, IGNORE_CHARACTERS } from '@/utils/formatter'
import { number2 } from '@/utils/index'

import { FilePreview } from '@/servers/Common'

// formatters
for (const formatName of Object.keys(formatMap)) {
  VxeUI.formats.add(formatName, {
    tableCellFormatMethod: formatMap[formatName],
    tableFooterCellFormatMethod: ({ itemValue }) => {
      return formatMap[formatName]({ cellValue: itemValue })
    },
  })
}

// renders
VxeUI.renderer.add('image', {
  renderTableDefault: imageRender,
})

VxeUI.renderer.add('imageList', {
  renderTableDefault: imageListRender,
})

VxeUI.renderer.add('copy', {
  renderTableDefault: copyRender,
})

VxeUI.renderer.add('tag', {
  renderTableDefault: (_, params) => {
    let value = params.row[params.column.field]
    const rawOptions = params.column.cellRender && params.column.cellRender.options
    const options: Record<string, any> = rawOptions && typeof rawOptions === 'object' && !Array.isArray(rawOptions) ? rawOptions : {}
    if (options.formatter) {
      value = options.formatter(value)
    }
    // 支持 value 为 string、array、object array
    let tags: any[] = []
    if (Array.isArray(value)) {
      tags = value
    } else if (typeof value === 'string') {
      tags = value ? value.split(',') : []
    } else if (typeof value === 'object' && value !== null) {
      tags = [value]
    } else if (value) {
      tags = [value]
    }

    // options: { colorMap, typeMap, shape, style, defaultType, defaultColor }
    const colorMap = options.colorMap || {}
    const typeMap = options.typeMap || {}
    const shape = options.shape || 'round' // round/square
    const customStyle = options.style || {}
    const defaultType = options.defaultType || 'default'
    const defaultColor = options.defaultColor || ''
    return h(
      'div',
      { class: 'flex overflow-hidden' },
      tags.map((item) => {
        let text = item
        let type = defaultType
        let color = defaultColor
        let style = { ...customStyle }
        if (typeof item === 'object' && item !== null) {
          text = item.text || item.label || item.value || ''
          type = item.type || typeMap[text] || defaultType
          color = item.color || colorMap[text] || defaultColor
          style = { ...style, ...(item.style || {}) }
        } else {
          type = typeMap[item] || defaultType
          color = colorMap[item] || defaultColor
        }
        if (color) {
          style.backgroundColor = color
          style.borderColor = color
          style.color = '#fff'
        }
        return h(
          'span',
          {
            class: ['easy-tag no-border cursor-default', type !== 'default' ? `easy-tag--${type}` : 'default', shape === 'square' ? 'easy-tag--square' : 'easy-tag--round'],
            style,
          },
          text,
        )
      }),
    )
  },
})

VxeUI.renderer.add('text', {
  renderTableDefault: textRender,
})

VxeUI.renderer.add('price', {
  renderTableDefault: (_, params) => {
    const { column, row } = params
    const cellRenderOptions: any = column.cellRender
    // column.showOverflow = 'ellipsis'
    if (IGNORE_CHARACTERS.includes(row[column.field])) return row[column.field]
    const value = Number(row[column.field] ?? 0).roundNext(cellRenderOptions?.precision || 0)
    const [int, decimal] = value.split('.')
    const found = (decimal || '').match(/[0]{3,}/)
    const valueRender = h(
      'span',
      {},
      found && found.index !== undefined
        ? [`${int === '0' ? 0 : number2(+int, 0)}.${decimal.slice(0, found.index)}0`, h('sub', {}, found[0].length), decimal.slice(found.index + found[0].length)]
        : value,
    )
    return cellRenderOptions?.render ? cellRenderOptions.render({ row, column, valueRender, cellValue: row[column.field] }) : valueRender
  },
})

VxeUI.renderer.add('status', {
  renderTableDefault: (_, params) => {
    const { column, row } = params
    const value = typeof column?.formatter === 'function' ? column.formatter({ cellValue: row[column.field], row, column }) : row[column.field] || ''

    if (IGNORE_CHARACTERS.includes(value)) return value
    if (!value) return ''

    let cssStr = 'default'
    if (/拒绝|超时|未通过/.test(value)) cssStr = 'error'
    else if (/级审核/.test(value)) cssStr = 'audit'
    else if (/未|待/.test(value)) cssStr = 'wait'
    else if (/默认|中|进行|部分/.test(value)) cssStr = 'info'
    else if (/即将/.test(value)) cssStr = 'warn'
    else if (/作废|取消|关闭/.test(value)) cssStr = 'disabled'
    else if (/通过|成功|完成|确认|完全|正常|已/.test(value)) cssStr = 'success'

    return h('span', { class: `easy-label-status ${cssStr}` }, value)
  },
})

VxeUI.renderer.add('file', {
  renderTableDefault: (_, params) => {
    const { column, row } = params
    const values = typeof column?.formatter === 'function' ? column.formatter({ cellValue: row[column.field], row, column }) : row[column.field] || ''
    const previewBtn = (row) => {
      if (!/\.(jpg|jpeg|pdf|ppt|png|doc)$/i.test(row.org_name)) {
        const id = row.id
        FilePreview({ fileId: id }).then((res) => {
          const url = res.data
          window.open(url, '_blank')
        })
      } else {
        window.open(row.url, '_blank')
      }
    }

    const statusMap = {
      2: {
        cls: 'icon-check-circle-outlined success-color',
        click: previewBtn,
      },
    }

    return h(
      'div',
      { class: 'flex items-center' },
      values.map((f) => {
        const { click, cls } = statusMap[f?.status || 2]
        const _name = f?.org_name || f
        const fileName = _name?.length > 10 ? _name.replace(/^(.{5}).*?(.{3}\..+)$/, '$1...$2') : _name
        return h(
          'div',
          {
            class: 'inline-flex mr-4',
          },
          [
            // in
            h(
              'span',
              {
                class: 'link',
                onClick: () => {
                  click(f)
                },
              },
              fileName,
            ),
            h('span', { class: `iconfont ml-4 ${cls}` }),
          ],
        )
      }),
    )
  },
})

export default VxeUI
