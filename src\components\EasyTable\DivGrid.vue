<template>
  <div class="div-grid" :class="{ border: props.border }" :style="{ minHeight: props.minHeight + 'px' }" @click.stop>
    <!-- header -->
    <div
      v-if="showHeader"
      class="div-grid-header"
      :style="{
        'grid-template-columns': `repeat(${girdColumns.length}, 0fr)`,
        'padding-left': leftConfig.pull + 'px',
      }"
    >
      <div
        class="div-grid-header-item absolute!"
        v-for="(column, columnIndex) in leftConfig.list"
        :key="column.field"
        :style="{ width: column.width + 'px', textAlign: column.align || 'left', ...setStyle(columnIndex, 'left') }"
      >
        <span>{{ column.title }}</span>
      </div>
      <div
        class="div-grid-header-item"
        v-for="column in girdColumns.filter((f) => !f.fixed)"
        :key="column.field"
        :style="{
          width: column.width + 'px',
          textAlign: column.align || 'left',
        }"
        @mouseleave="mouseleaveByItem"
      >
        <span :title="column.title">{{ column.title }}</span>
        <ExclamationCircleOutlined v-if="column.tooltip" class="ml-5 cursor-pointer" @mouseenter="(e) => mouseenterByItem(e, column)" />
      </div>
      <div class="div-grid-header-item" v-for="column in girdColumns.filter((f) => f.fixed === 'right')" :key="column.field" :style="{ width: column.width + 'px', textAlign: column.align || 'left' }">
        {{ column.title }}
      </div>
    </div>
    <!-- body -->
    <div v-if="gridData?.length" class="div-grid-body bg-#fff" :style="{ 'padding-left': leftConfig.pull + 'px', maxHeight: Math.min(props.height, innerHeight) + 'px' }">
      <div
        class="div-grid-body-row"
        v-for="(row, index) in gridData"
        :key="row[props.keyField || '']"
        :style="{
          'grid-template-columns': `repeat(${girdColumns.length}, 0fr)`,
        }"
      >
        <div
          class="div-grid-body-item absolute!"
          v-for="(column, columnIndex) in leftConfig.list"
          :key="column.field"
          :style="{
            width: column.width + 'px',
            height: `${props.rowHeight}px`,
            ...setStyle(columnIndex, 'left'),
            textAlign: column.align || 'left',
            justifyContent: getJustifyContent(column.align),
          }"
        >
          <span v-if="column.type === 'seq'">{{ `${props.index ? `${props.index}-` : ''}${index + 1}` }}</span>
          <template v-else-if="column.cellRender || column.render">
            <component :is="cellRender(column, row, index)"></component>
          </template>
          <span v-else :style="column.style || {}" @mouseenter="(e) => mouseenterByItem(e, column, row)" @mousemove="(e) => mouseenterByItem(e, column, row)" @mouseleave="mouseleaveByItem">
            {{ renderItemCell({ row, column }) }}
          </span>
        </div>
        <div
          class="div-grid-body-item"
          v-for="column in girdColumns.filter((f) => !f.fixed || f.fixed === 'right')"
          :key="column.field"
          :style="{
            width: column.width + 'px',
            height: `${props.rowHeight}px`,
            textAlign: column.align || 'left',
            justifyContent: getJustifyContent(column.align),
          }"
        >
          <template v-if="column.cellRender || column.render">
            <component :is="cellRender(column, row, index)"></component>
          </template>
          <span :style="column.style || {}" @mouseenter="(e) => mouseenterByItem(e, column, row)" @mousemove="(e) => mouseenterByItem(e, column, row)" @mouseleave="mouseleaveByItem" v-else>
            {{ renderItemCell({ row, column }) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

import { formatMap } from '@/utils/formatter'
import { imageRender, imageByIdRender, textRender, copyRender } from '@/utils/VxeRender'

const props = withDefaults(
  defineProps<{
    columns?: Array<any>
    data?: Array<any>
    keyField?: string
    rowHeight?: number
    index?: number
    height?: number
    minHeight?: number
    parentRow?: any
    border?: boolean
    showHeader?: boolean
  }>(),
  {
    columns: () => [],
    data: () => [],
    rowHeight: 32,
    height: 100,
    minHeight: 50,
    border: false,
    showHeader: true,
  },
)

const innerHeight = ref(window.innerHeight - 100)

const tooltip: any = inject('tooltip')
// 根据对齐方式获取justify-content值
const getJustifyContent = (align: string) => {
  switch (align) {
    case 'center':
      return 'center'
    case 'right':
      return 'flex-end'
    case 'left':
    default:
      return 'flex-start'
  }
}

const cellFormatter = (row, column) => {
  if (!column.formatter) return undefined
  const formatter = column.formatter && (typeof column.formatter === 'string' ? formatMap[column.formatter] : column.formatter)
  return formatter({ cellValue: row[column.field], value: row[column.field], row })
}

const mouseenterByItem = (event, column, row?: Record<string, any>) => {
  if (row) {
    const node = event.target.parentNode
    if (node.scrollWidth > node.clientWidth) {
      tooltip.open(
        {
          target: node,
          type: event.type,
        },
        cellFormatter(row, column) ?? row[column.field],
      )
    }
  } else {
    tooltip.open(
      {
        target: event.target.parentNode,
        type: event.type,
      },
      column.tooltip,
    )
  }
}
const mouseleaveByItem = () => {
  if (tooltip && typeof tooltip.close === 'function') {
    tooltip.close()
  }
}

const girdColumns = ref<Record<string, any>[]>([])
watch(
  () => props.columns,
  () => {
    girdColumns.value = props.columns
  },
  { immediate: true },
)
const gridData = ref<Record<string, any>[]>(props.data)
watch(
  () => props.data,
  () => {
    gridData.value = props.data
  },
  { immediate: true },
)
const leftConfig = computed(() => {
  const list = girdColumns.value.filter((i) => i.fixed == 'left')
  return {
    list,
    length: list.length,
    pull: list.reduce((acc, cur) => {
      return acc + cur.width
    }, 0),
  }
})

const cellRender = (column, row, index) => {
  const target = {
    image: imageRender,
    imageById: imageByIdRender,
    text: textRender,
    copy: copyRender,
  }[column?.cellRender?.name]
  return {
    render: () => {
      return target
        ? target(undefined, {
            row,
            column,
            parentRow: props.parentRow,
          })
        : column.render
          ? column.render({ row, column, parentRow: props.parentRow, rowIndex: index })
          : null
    },
  }
}

const renderItemCell = ({ column, row }) => {
  return cellFormatter(row, column) ?? row[column.field]
}

const setStyle = (index, direction) => {
  return {
    [direction]: `${leftConfig.value.list.slice(0, index).reduce((acc, cur) => {
      return acc + cur.width
    }, 0)}px`,
    zIndex: 100,
  }
}

const updateGrid = ({ columns, data }) => {
  if (columns) {
    girdColumns.value = columns
  }
  if (data) {
    gridData.value = data
  }
}

onBeforeUnmount(() => {
  mouseleaveByItem()
})

defineExpose({ updateGrid })
</script>

<style lang="scss" scoped>
.div-grid {
  color: var(--vxe-ui-font-color);
  box-shadow: 0 0 3px rgb(0 0 0 / 20%) inset;

  &.border {
    .div-grid-header-item {
      border-top: 1px #eaecee solid;

      &:first-child {
        border-left: 1px #eaecee solid;
      }
    }

    .div-grid-body-item {
      display: flex;
      align-items: center;

      &:first-child {
        border-left: 1px #eaecee solid;
      }
    }
  }

  &-header {
    @apply grid relative;

    &-item {
      z-index: 1;
      padding-right: var(--vxe-ui-table-cell-padding-right);
      padding-left: var(--vxe-ui-table-cell-padding-left);
      font-weight: bold;
      color: rgb(0 0 0 / 75%);
      background-color: #f8f8f9;
      border-right: 1px #eaecee solid;
      border-bottom: 1px #eaecee solid;

      @apply relative h-30 pr-5 overflow-hidden whitespace-nowrap text-ellipsis lh-30;
    }
  }

  &-body {
    &-row {
      @apply grid;
    }

    &-item {
      z-index: 1;
      padding: 5px var(--vxe-ui-table-cell-padding-right) 5px var(--vxe-ui-table-cell-padding-left);
      border-right: 1px #eaecee solid;
      border-bottom: 1px #eaecee solid;

      @apply relative overflow-hidden whitespace-nowrap text-ellipsis lh-18;
    }
  }
}
</style>
