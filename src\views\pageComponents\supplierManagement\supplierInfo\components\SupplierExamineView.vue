<template>
  <a-drawer width="1000" @close="handleClose" v-model:open="openDrawer" :maskClosable="!isExamine">
    <template #title>供应商{{ isExamine ? '审核' : '详情' }}</template>
    <template #extra>
      <a-button @click="handleAuditLog">审核记录</a-button>
    </template>
    <div class="drawer-title">基本信息</div>
    <a-form :model="ExamineInfo" :label-col="{ style: { width: '120px' } }" :wrapper-col="{ span: 17 }">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="供应商编码">
            <span>{{ ExamineInfo.number }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="证件编号">
            <span>{{ ExamineInfo.credit_id }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="统一社会信用代码">
            <span>
              {{ ExamineInfo.credit_code }}
              <span>可在企信网上查询，</span>
              <a-button type="link" @click="openEnterpriseNet" class="p-0">去查询</a-button>
            </span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="供应商名称">
            <span>{{ ExamineInfo.supplier_name }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="营业执照有效期">
            <span v-if="ExamineInfo.is_long">长期</span>
            <span v-else>{{ ExamineInfo.business_license_validity?.slice(0, 10) }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="公司类型">
            <span>{{ ExamineInfo.company_type_string }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="成立日期">
            <span>{{ ExamineInfo.establishment_date?.slice(0, 10) }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="法人">
            <span>{{ ExamineInfo.legal_person_name }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="经营规模">
            <span>{{ ExamineInfo.business_scale_string }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="证件类型">
            <span>{{ ExamineInfo.certificate_type_string }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="营业执照">
            <a-button type="link" class="p-0" @click="certificatePreview(ExamineInfo.business_license_file_id)">预览</a-button>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="证件号">
            <span>{{ ExamineInfo.certificate_number }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="法人证件">
            <a-button type="link" class="p-0" @click="certificatePreview([ExamineInfo.id_card_front_file_id, ExamineInfo.id_card_back_file_id])">预览</a-button>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="营业执照地址">
            <span>{{ ExamineInfo.business_license_province + ExamineInfo.business_license_city + ExamineInfo.business_license_area + ExamineInfo.business_license_address }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="16">
          <a-form-item label="办公地址">
            <span>{{ ExamineInfo.officeAddress }}</span>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="drawer-title">公司扩展信息</div>
    <a-form :model="ExamineInfo" :label-col="{ style: { width: '120px' } }" :wrapper-col="{ span: 17 }">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="供应商类型">
            <span>{{ ExamineInfo.supplier_type_string }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="主营类目">
            <span v-for="(item, index) in ExamineInfo.main_categories_strings" :key="item">{{ item }}{{ index !== ExamineInfo.main_categories_strings.length - 1 ? '，' : '' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="自营/合作工厂规模">
            <span>{{ ExamineInfo.factory_scale_string }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="主营区域">
            <span v-for="(item, index) in ExamineInfo.main_regions_strings" :key="item">{{ item }}{{ index !== ExamineInfo.main_regions_strings.length - 1 ? '，' : '' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="工厂人员规模">
            <span>{{ ExamineInfo.factory_employee_count || 0 }}人</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="主营商品">
            <span class="break-words">{{ ExamineInfo.main_products }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="SKU数量">
            <span>{{ ExamineInfo.sku_count || 0 }}个</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="公司年销售额">
            <span>{{ ExamineInfo.annual_sales }}万元</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="主营商品价格区间">
            <span>{{ ExamineInfo.main_products_min_price + '-' + ExamineInfo.main_products_max_price }}元</span>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="drawer-title">联系人信息</div>
    <a-table :columns="contactInfoColumns" :data-source="ExamineInfo.srs_supplier_contact_infos" size="small" bordered :pagination="false" class="mb-16px">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'mobile_phone_number' && isExamine">{{ record.mobile_phone_number }}</template>
        <template v-if="column.dataIndex === 'mobile_phone_number' && !isExamine">
          <span>{{ record.mobile_phone_number }}</span>
          <br v-if="!record._phoneRevealed" />
          <a-button type="link" class="p-0 c-gray" @click="getContactPhone(record)" v-if="!record._phoneRevealed">点击查看监控字段，系统会记录点击操作</a-button>
        </template>
      </template>
    </a-table>
    <div class="drawer-title">财务信息</div>
    <a-form :model="ExamineInfo" :label-col="{ style: { width: '150px' } }" :wrapper-col="{ span: 17 }">
      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item label="结算方式">
            <span>{{ ExamineInfo.settlement_method_string }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="发票类型">
            <span>{{ ExamineInfo.invoice_type_string }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="默认税率">
            <span>{{ ExamineInfo.default_tax_rate_string }}</span>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-table :columns="financeInfoColumns" :data-source="ExamineInfo.srs_supplier_finance_infos" size="small" bordered :pagination="false" class="mb-16px">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'collection_card_number' && isExamine">{{ record.collection_card_number }}</template>
        <template v-if="column.dataIndex === 'collection_card_number' && !isExamine">
          <span>{{ record.collection_card_number }}</span>
          <br v-if="!record._bankCardRevealed" />
          <a-button type="link" class="p-0 c-gray" @click="getBankCard(record)" v-if="!record._bankCardRevealed">点击查看监控字段，系统会记录点击操作</a-button>
        </template>
      </template>
    </a-table>

    <div class="drawer-title">证书文件</div>
    <a-table :columns="certificateInfoColumns" :data-source="ExamineInfo.license_files" size="small" bordered :pagination="false" class="mb-16px">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-button type="link" @click="certificatePreview(record.id, 2)">预览</a-button>
        </template>
      </template>
    </a-table>
    <div v-if="!isExamine">
      <div class="drawer-title">其他信息</div>
      <a-form :model="ExamineInfo" :label-col="{ style: { width: '150px' } }" :wrapper-col="{ span: 17 }">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="创建人">
              <span>{{ ExamineInfo.creator_name }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="创建时间">
              <span>{{ ExamineInfo.create_at }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="更新人">
              <span>{{ ExamineInfo.modifier_name }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="更新时间">
              <span>{{ ExamineInfo.modified_at }}</span>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <a-form :model="ExamineInfo" :label-col="{ style: { width: '627px' } }" :wrapper-col="{ span: 17 }">
        <a-form-item label="供应商加入时间">
          <span>{{ ExamineInfo.join_at }}</span>
        </a-form-item>
      </a-form>
    </div>
    <a-image-preview-group
      :style="{ display: 'none' }"
      :preview="{
        visible: imageVisible,
        onVisibleChange: setVisible,
      }"
    >
      <template v-for="(imgitem, imgindex) in previewUrl" :key="imgindex">
        <a-image :width="0" :src="imgitem" />
      </template>
    </a-image-preview-group>
  </a-drawer>
  <AuditLogDrawer ref="auditLogDrawerRef" />
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import { ref, onMounted } from 'vue'
import { GetSupplierInfo, GetContactPhone, GetBankCard } from '@/servers/supplierSettlementApproval'
import { defEmptyStr } from '@/utils/index'
import AuditLogDrawer from "./AuditLogDrawer.vue"

const userData = ref()
const href = ref()
const VITE_APP_ENV = ref('')
const openDrawer = ref(false)

// 预览url
const previewUrl = ref<Array<string>>([])
const imageVisible = ref(false)
const setVisible = (visible: boolean) => {
  imageVisible.value = visible
}
const ExamineInfo = ref<any>({})
const ExamineId = ref<number>(0)
const auditLogDrawerRef = ref()
// 判断是详情还是审核
const isExamine = ref(false)
const contactInfoColumns = ref<any[]>([
  {
    title: '联系人姓名',
    dataIndex: 'name',
    align: 'center',
  },
  {
    title: '职务',
    dataIndex: 'job',
    align: 'center',
  },
  {
    title: '手机号',
    dataIndex: 'mobile_phone_number',
    align: 'center',
    width: '250px',
    ellipsis: true,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '微信',
    dataIndex: 'weixin_number',
    align: 'center',
  },
  {
    title: '默认联系人',
    dataIndex: 'is_default',
    align: 'center',
    customRender: ({ text }: { text: boolean }) => (text ? '是' : '否'),
  },
])
const financeInfoColumns = ref<any[]>([
  {
    title: '账户名称',
    dataIndex: 'account_name',
    align: 'center',
  },
  {
    title: '账户类型',
    dataIndex: 'account_type',
    align: 'center',
    customRender: ({ text }: { text: number }) => {
      if (text === 1) {
        return '公户'
      }
      if (text === 2) {
        return '私户'
      }
    },
  },
  {
    title: '银行卡号',
    dataIndex: 'collection_card_number',
    align: 'center',
  },
  {
    title: '开户行',
    dataIndex: 'collection_bank',
    align: 'center',
  },
  {
    title: '所属支行',
    dataIndex: 'collection_bank_branch',
    align: 'center',
  },
  {
    title: '是否默认',
    dataIndex: 'is_default',
    align: 'center',
    customRender: ({ text }: { text: boolean }) => (text ? '是' : '否'),
  },
])
const certificateInfoColumns = ref<any[]>([
  {
    title: '文件',
    dataIndex: 'original_name',
    align: 'center',
  },
  {
    title: '上传人',
    dataIndex: 'account_name',
    align: 'center',
  },
  {
    title: '上传时间',
    dataIndex: 'create_at',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
  },
])
const handleClose = () => {
  openDrawer.value = false
}
const showDrawer = (id: number, type: string) => {
  openDrawer.value = true
  if (type === 'view') {
    isExamine.value = false
  } else {
    isExamine.value = true
  }
  ExamineId.value = id
  getSupplierInfo()
}
// 获取供应商信息
const getSupplierInfo = async () => {
  const params = {
    id: ExamineId.value,
    preview: !isExamine.value,
  }
  const res = await GetSupplierInfo(params)
  ExamineInfo.value = res.data
  ExamineInfo.value.officeAddress =
    defEmptyStr(ExamineInfo.value.office_address_province) +
    defEmptyStr(ExamineInfo.value.office_address_city) +
    defEmptyStr(ExamineInfo.value.office_address_area) +
    defEmptyStr(ExamineInfo.value.office_address_detail)
}
// 打开企信网
const openEnterpriseNet = () => {
  window.open('https://www.gsxt.gov.cn/index.html', '_blank')
}
// 预览
const certificatePreview = async (id: number | number[], type = 1) => {
  console.log('window', window.location)
  previewUrl.value = []
  setVisible(false)
  let url: string[] = []
  if (Array.isArray(id)) {
    id.forEach((item) => {
      if (VITE_APP_ENV.value == 'development') {
        url.push(`${window.location.origin}/api/api/Files/ViewByFileId?fileId=${item}`)
      } else {
        url.push(`${window.location.origin}/api/Files/ViewByFileId?fileId=${item}`)
      }
    })
  } else {
    if (VITE_APP_ENV.value == 'development') {
      url = [`${window.location.origin}/api/api/Files/ViewByFileId?fileId=${id}`]
    } else {
      url = [`${window.location.origin}/api/Files/ViewByFileId?fileId=${id}`]
    }
  }

  console.log('url', url)
  const arr: any = []
  try {
    url.forEach(async (item) => {
      const response = await fetch(item, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/octet-stream',
          logintoken: userData.value.login_token,
        },
      })
      console.log('response11', response)

      if (!response.ok) {
        message.warning('获取失败')
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const blob = await response.blob()
      const previewImageUrl = URL.createObjectURL(blob)
      previewUrl.value.push(previewImageUrl)
      if (typeof id === 'number' && type !== 1) {
        window.open(previewUrl.value[0], '_blank')
        setTimeout(() => URL.revokeObjectURL(previewUrl.value[0]), 30000)
      }
      console.log(previewImageUrl)
    })

    // previewUrl.value = arr
    console.log('previewUrl', previewUrl.value)
    if (type == 1) {
      imageVisible.value = true
      return
    }
    // 在新窗口打开预览

    // 可选：一段时间后释放内存

    return arr[0]
  } catch (error) {
    console.error('转换失败:', error)
    throw error
  }
}

// 获取联系人真实电话号码
const getContactPhone = async (row: any) => {
  const res = await GetContactPhone({ id: row.id, supplier_id: row.supplier_id })
  // 找到对应的联系人并替换手机号
  const contactList = ExamineInfo.value.srs_supplier_contact_infos
  const idx = contactList.findIndex((item: any) => item.id === row.id)
  if (idx !== -1) {
    contactList[idx].mobile_phone_number = res.data.mobile_phone_number
    contactList[idx]._phoneRevealed = true
  }
}
// 获取真实银行卡号
const getBankCard = async (row: any) => {
  const res = await GetBankCard({ id: row.id, supplier_id: row.supplier_id })
  // 找到相对于的银行卡号
  const bankCardList = ExamineInfo.value.srs_supplier_finance_infos
  const idx = bankCardList.findIndex((item: any) => item.id === row.id)
  if (idx !== -1) {
    bankCardList[idx].collection_card_number = res.data.collection_card_number
    bankCardList[idx]._bankCardRevealed = true
  }
}

// 打开审核记录
const handleAuditLog = () => {
  auditLogDrawerRef.value.open(ExamineId.value)
}

onMounted(() => {
  href.value = window.location.origin
  VITE_APP_ENV.value = import.meta.env.VITE_APP_ENV
  const userDatastr = localStorage.getItem('userData') || ''
  userData.value = userDatastr != '' ? JSON.parse(userDatastr) : {}
})
defineExpose({
  showDrawer,
})
</script>
