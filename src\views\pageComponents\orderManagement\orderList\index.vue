<template>
  <div class="main">
    <!-- 筛选表单 -->
    <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.ORDER_LIST" @search="search" @setting="tableRef?.showTableSetting()" />

    <!-- 表格 -->
    <BaseTable
      ref="tableRef"
      :isCheckbox="true"
      :pageType="PageType.ORDER_LIST"
      :get-list="getListFn"
      v-model:form="formArr"
      :isIndex="true"
      :form-format="formFormat"
      :data-format="dataFormat"
      :footer-data="footerData"
      :show-footer="showFooter"
    >
      <!-- 左侧按钮 -->
      <template #left-btn>
        <a-button type="primary" @click="showSyncModal">同步数据</a-button>
      </template>

      <!-- 主图列 -->
      <template #order_items="{ row, column }">
        <component v-if="row.order_items && row.order_items.length > 0" :is="orderItemsListRender({ column, row, rowHeight: 50 })"></component>
        <div v-else class="order-items-empty">
          <img src="/src/assets/icons/error-image.svg" alt="默认图片" class="default-image" />
        </div>
      </template>

      <!-- 收货信息列 -->
      <template #shipping_info="{ row }">
        <div v-if="row.shipping_info" class="shipping-info">
          <div>收货人：{{ row.shipping_info.recipient || '--' }}</div>
          <div>手机号：{{ formatPhone(row.shipping_info.telephone) || '--' }}</div>
          <div>收货地址：{{ row.shipping_info.address_info || '--' }}</div>
        </div>
        <span v-else>--</span>
      </template>

      <!-- 异常原因列 -->
      <template #question_type="{ row }">
        <span>{{ getQuestionTypeDisplay(row) }}</span>
      </template>

      <!-- 操作列 -->
      <template #operate="{ row }">
        <a-button type="text" @click="viewDetail(row)">查看</a-button>
      </template>
    </BaseTable>
  </div>
  <!-- 同步数据弹窗 -->
  <a-modal v-model:open="syncModalVisible" title="同步订单数据" :confirm-loading="syncLoading" :maskClosable="false" @ok="handleSync" ok-text="确认同步" cancel-text="取消">
    <a-form ref="syncFormRef" :model="syncForm" :rules="syncRules" layout="vertical">
      <a-form-item label="开始日期" name="startDate">
        <a-date-picker
          v-model:value="syncForm.startDate"
          :disabled-date="disabledStartDate"
          style="width: 100%"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          placeholder="请选择开始日期"
          @change="onStartDateChange"
        />
      </a-form-item>
      <a-form-item label="结束日期" name="endDate">
        <a-date-picker
          v-model:value="syncForm.endDate"
          :disabled-date="disabledEndDate"
          style="width: 100%"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          placeholder="请选择结束日期"
          :disabled="!syncForm.startDate"
        />
      </a-form-item>
      <div class="text-gray-400 text-xs">
        <p>提示：</p>
        <p>1. 同步时间范围不能超过7天</p>
        <p>2. 同步时间视数据量而定，请耐心等候</p>
      </div>
    </a-form>
  </a-modal>

  <!-- 销售订单详情抽屉 -->
  <SaleOrderDetailDrawer ref="detailDrawerRef" />
</template>

<script setup lang="ts">
import { ref, onMounted, h } from 'vue'
import { message, Modal } from 'ant-design-vue'

import BaseTable from '@/components/BaseTable/index.vue'
import SearchForm from '@/components/SearchForm/index.vue'
import { imageListRender } from '@/utils/VxeRender'
import DivGrid from '@/components/EasyTable/DivGrid.vue'
import { PageType } from '@/common/enum'
import { GetOrderList, ManualSyncOrder } from '@/servers/OrderManage'
import { GetSupplierDropdownData } from '@/servers/Supplier'
import dayjs from 'dayjs'
import { getCommonOption } from '@/utils'
import SaleOrderDetailDrawer from '../components/SaleOrderDetailDrawer.vue'

// 表格引用
const tableRef = ref()
const formRef = ref()
const detailDrawerRef = ref()

// 同步相关状态
const syncModalVisible = ref(false)
const syncLoading = ref(false)
const syncFormRef = ref()
const syncForm = ref({
  startDate: null,
  endDate: null,
})

// 同步表单校验规则
const syncRules = {
  startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
  endDate: [
    { required: true, message: '请选择结束日期', trigger: 'change' },
    {
      validator: (_rule: any, value: any) => {
        if (!value || !syncForm.value.startDate) return Promise.resolve()
        const diffDays = dayjs(value).diff(dayjs(syncForm.value.startDate), 'day')
        if (diffDays > 6) {
          return Promise.reject(new Error('同步时间范围不能超过7天'))
        }
        return Promise.resolve()
      },
      trigger: 'change',
    },
  ],
}

// 显示同步弹窗
const showSyncModal = () => {
  syncForm.value.startDate = null
  syncForm.value.endDate = null
  syncModalVisible.value = true
}

// 开始日期变化处理
const onStartDateChange = () => {
  // 当开始日期变化时，清空结束日期
  syncForm.value.endDate = null
}

// 禁用开始日期
const disabledStartDate = (current: any) => {
  // 禁用未来日期
  return current && current > dayjs().endOf('day')
}

// 禁用结束日期
const disabledEndDate = (current: any) => {
  // 禁用未来日期
  if (current && current > dayjs().endOf('day')) {
    return true
  }

  // 如果已选择开始日期，限制结束日期范围
  if (syncForm.value.startDate) {
    const startDate = dayjs(syncForm.value.startDate)
    // 禁用开始日期之前的日期
    if (current.isBefore(startDate, 'day')) {
      return true
    }
    // 禁用超过7天的日期
    if (current.isAfter(startDate.add(6, 'day'), 'day')) {
      return true
    }
  }

  return false
}

// 确认同步
const handleSync = () => {
  syncFormRef.value
    ?.validate()
    .then(async () => {
      const { startDate, endDate } = syncForm.value

      // 验证时间范围
      if (!startDate || !endDate) {
        message.error('请完善同步时间，建议不要太长')
        return
      }

      const diffDays = dayjs(endDate).diff(dayjs(startDate), 'day')
      if (diffDays > 6) {
        message.error('同步时间范围不能超过7天')
        return
      }

      // 二次确认弹窗
      const confirmed = await new Promise((resolve) => {
        Modal.confirm({
          title: '确认同步数据',
          content: '是否确认同步数据？同步时间视数据量而定，请耐心等候',
          okText: '确认',
          cancelText: '取消',
          onOk: () => resolve(true),
          onCancel: () => resolve(false),
        })
      })

      if (!confirmed) return

      try {
        syncLoading.value = true
        await ManualSyncOrder({
          start_time: dayjs(startDate).format('YYYY-MM-DD'),
          end_time: dayjs(endDate).format('YYYY-MM-DD'),
        })
        message.success('任务执行中，请稍后查看')
        syncModalVisible.value = false
        // 刷新列表
        // tableRef.value?.search()
      } catch (error: any) {
        console.error('同步失败:', error)

        // 检查是否有执行中的同步任务
      } finally {
        syncLoading.value = false
      }
    })
    .catch(() => {
      // 表单验证失败
      message.error('请完善同步时间，建议不要太长')
    })
}

// 合计行数据
const footerData = ref([{ seq: '合计', total_amount: '￥0.00' }])
const showFooter = ref(true)

// 供应商数据
const supplierOptions = ref<any[]>([])

// 供应商选项配置
const supplierOptionConfig = {
  labelField: 'supplier_name',
  valueField: 'supplier_id',
  searchFields: ['supplier_id', 'supplier_number', 'supplier_name'],
  labelFormatter: (item: any) => `${item.supplier_name} (${item.supplier_number})`,
}

// 订单商品表格列配置
const orderItemColumns = ref([
  {
    title: '序号',
    field: 'index',
    width: 60,
    align: 'center',
  },
  {
    title: '主图',
    field: 'main_images_id',
    width: 80,
    align: 'center',
    cellRender: { name: 'imageById' },
  },
  {
    title: '商品名称',
    field: 'product_name',
    width: 300,
    align: 'left',
  },
  {
    title: '所属供应商',
    field: 'supplier',
    width: 200,
    align: 'center',
  },
  {
    title: '平台商品编码',
    field: 'product_number',
    width: 150,
    align: 'left',
    cellRender: {
      name: 'copy',
    },
  },
  {
    title: '规格型号',
    field: 'spec_info',
    width: 120,
    align: 'left',
  },
  {
    title: '数量',
    field: 'qty',
    width: 80,
    align: 'right',
  },
  {
    title: '采购单价（含税）',
    field: 'store_supply_price',
    width: 100,
    align: 'right',
  },
  {
    title: '合计金额',
    field: 'total_amount',
    width: 100,
    align: 'right',
  },
  {
    title: '可用库存',
    field: 'inventory_num',
    width: 100,
    align: 'right',
  },
])

// 筛选表单配置
const formArr = ref([
  {
    key: 'order_number',
    label: '订单编号',
    type: 'batch-input',
    value: '',
    isShow: true,
    placeholder: '请输入订单编号，回车批量筛选',
  },

  {
    key: 'product_number',
    label: '平台商品编号',
    type: 'batch-input',
    value: '',
    isShow: true,
    placeholder: '请输入平台商品编号，回车批量筛选',
  },
  {
    key: 'supplier_id',
    label: '供应商名称/编号',
    type: 'dropdown-search-select' as const,
    value: null,
    isShow: true,
    placeholder: '请选择或搜索供应商',
    class: 'w-200',
    options: [],
    optionConfig: supplierOptionConfig,
  },
  {
    key: 'product_name',
    label: '商品名称',
    type: 'input',
    value: '',
    isShow: true,
    placeholder: '请输入商品名称',
  },
  {
    key: 'question_type',
    label: '异常原因',
    type: 'input',
    value: '',
    isShow: true,
    placeholder: '请输入异常原因',
  },
  // {
  //   key: 'shop_buyer_id',
  //   label: '买家账号',
  //   type: 'input',
  //   value: '',
  //   isShow: true,
  //   placeholder: '请输入买家账号',
  // },
  {
    key: 'tracking_number',
    label: '物流单号',
    type: 'input',
    value: '',
    isShow: true,
    placeholder: '请输入物流单号',
  },

  {
    key: 'order_create_at',
    label: '下单时间',
    type: 'range-picker',
    value: [],
    isShow: true,
    placeholder: ['下单开始时间', '下单结束时间'],
    formKeys: ['order_create_at_start', 'order_create_at_end'],
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
  },

  {
    key: 'pay_date',
    label: '付款时间',
    type: 'range-picker',
    value: [],
    isShow: true,
    placeholder: ['付款开始时间', '付款结束时间'],
    formKeys: ['pay_date_start', 'pay_date_end'],
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
  },

  {
    key: 'finish_date',
    label: '完成时间',
    type: 'range-picker',
    value: [],
    isShow: true,
    placeholder: ['完成开始时间', '完成结束时间'],
    formKeys: ['finish_date_start', 'finish_date_end'],
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
  },
  {
    key: 'order_status',
    label: '订单状态',
    type: '',
    value: '',
    isQuicks: true,
    isShow: true,
    placeholder: '请选择订单状态',
    options: [],
  },
  {
    key: 'question_type_attr',
    label: '异常状态',
    type: '',
    isQuicks: true,
    value: '',
    isShow: true,
    placeholder: '请输入错误状态',
    options: [],
  },
])

// 搜索
const search = () => {
  tableRef.value?.search()
}

// 表单格式化
const formFormat = (params: any) => {
  const formattedParams = {
    ...params,
    is_page: true,
    is_get_total: true,
    is_get_total_only: false,
  }

  // 处理日期范围参数
  formArr.value.forEach((item: any) => {
    if (item.formKeys && item.value && Array.isArray(item.value) && item.value.length === 2) {
      formattedParams[item.formKeys[0]] = item.value[0]
      formattedParams[item.formKeys[1]] = item.value[1]
    }
  })

  return formattedParams
}

// 数据格式化
const dataFormat = (data: any[]) => {
  return data.map((item: any) => ({
    ...item,
    // 格式化时间显示
    order_create_at: item.order_create_at ? dayjs(item.order_create_at).format('YYYY-MM-DD HH:mm:ss') : '--',
    pay_date: item.pay_date ? dayjs(item.pay_date).format('YYYY-MM-DD HH:mm:ss') : '--',
    finish_date: item.finish_date ? dayjs(item.finish_date).format('YYYY-MM-DD HH:mm:ss') : '--',
  }))
}

// 获取列表数据
const getListFn = async (params: any) => {
  // 添加获取合计数据的参数
  params.is_get_total = true

  const res = await GetOrderList(params)

  // 计算合计金额 - 统计订单级别的total_amount
  if (res?.data?.list && Array.isArray(res.data.list)) {
    let totalAmount = 0
    res.data.list.forEach((order: any) => {
      totalAmount += Number(order.total_amount || 0)
    })

    // 更新合计行数据
    footerData.value = [{ seq: '合计', total_amount: totalAmount.toFixed(2) }]
    showFooter.value = true
  }

  return res
}

// 格式化手机号
const formatPhone = (phone: string) => {
  if (!phone) return '--'
  if (phone.length <= 7) return phone
  return phone.replace(/(\d{3})\d*(\d{4})/, '$1****$2')
}

// 获取异常原因显示内容
const getQuestionTypeDisplay = (row: any) => {
  // 获取异常状态的值（从订单状态选项中查找标签为"异常"的选项）
  const orderStatusItem = formArr.value.find((item: any) => item.key === 'order_status')
  const exceptionStatusOption = orderStatusItem?.options?.find((option: any) => option.label === '异常')
  const exceptionStatusValue = (exceptionStatusOption as any)?.value

  if (row.order_status === exceptionStatusValue) {
    return row.question_type || '--'
  }

  // 其他情况都显示 "--"
  return '--'
}

// 订单商品列表渲染器
const orderItemsListRender = ({ column, row, rowHeight }: any) => {
  // 准备订单商品数据，添加序号和图片URL
  const orderItemsData = (row.order_items || []).map((item: any, index: number) => ({
    ...item,
    index: index + 1,
    main_image_url: null, // 将在DivGrid中处理
    spec_info: item.spec_info || '--',
    store_supply_price: item.store_supply_price || 0,
    total_amount: item.total_amount || 0,
    inventory_num: item.inventory_num || 0,
  }))

  return imageListRender(null, {
    key: 'order_items',
    column,
    row,
    size: rowHeight || 50,
    options: {
      width: '60vw',
      render: ({ maxHeight }: any) =>
        h(
          'div',
          { class: 'overflow-auto' },
          h(DivGrid, {
            columns: orderItemColumns.value,
            data: orderItemsData,
            rowHeight: 46,
            height: Math.min(maxHeight, orderItemsData.length * 46 + 30),
            border: true,
            keyField: 'id',
          }),
        ),
    },
  })
}

// 查看详情
const viewDetail = (row: any) => {
  // 实现查看详情功能
  const ids = tableRef.value?.tableData.map((i: any) => i.id)
  const uniqueIds = [...new Set(ids)] as number[]
  detailDrawerRef.value?.show(row.id, uniqueIds)
}
// 获取供应商数据
const getSupplierOptions = async () => {
  try {
    const res = await GetSupplierDropdownData()

    if (res.success && res.data) {
      supplierOptions.value = res.data

      // 更新表单配置中的供应商选择器
      const supplierItem = formArr.value.find((item: any) => item.key === 'supplier_id')

      if (supplierItem) {
        supplierItem.options = res.data
        supplierItem.optionConfig = supplierOptionConfig
      }
    }
  } catch (error) {
    console.error('获取供应商数据失败:', error)
  }
}

// 获取订单状态和异常状态选项
const getOrderOptions = async () => {
  try {
    // 获取订单状态(28)和异常状态(29)的选项数据
    const [orderStatusOptions, exceptionStatusOptions] = await getCommonOption([28, 29])

    formArr.value.forEach((item: any) => {
      if (item.key === 'order_status') {
        // 订单状态下拉数据
        item.options = orderStatusOptions || []
      }
      if (item.key === 'question_type_attr') {
        // 异常状态下拉数据
        item.options = exceptionStatusOptions || []
      }
    })
  } catch (error) {
    console.error('获取订单选项失败:', error)
  }
}

// 注意：供应商搜索功能已移至FormSupplierSearch组件内部处理

onMounted(() => {
  // 页面加载时获取选项数据并自动搜索
  getOrderOptions()
  getSupplierOptions()
  // 页面加载时自动搜索
  search()
})
</script>

<style scoped lang="scss">
.main {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.shipping-info {
  font-size: 12px;
  line-height: 1.4;

  div {
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.order-items-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;

  .default-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;

    // border: 1px solid #d9d9d9;
  }
}

// 复制按钮样式
:deep(.copy-cell) {
  &:hover .copy-cell-btn {
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
  }

  .copy-cell-btn {
    display: none;
    cursor: pointer;

    &:hover {
      color: #40a9ff;
    }
  }
}
</style>
